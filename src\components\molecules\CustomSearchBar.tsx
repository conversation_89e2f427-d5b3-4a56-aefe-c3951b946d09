import React from 'react';
import {
    View,
    TextInput,
    StyleSheet,
    TouchableOpacity,
    Image,
} from 'react-native';
import COLORS from '../../themes/colors';
import { hp, wp } from '../../utils/helper';
import { SEARCH_ICON } from '../../utils/image-constants';

const CustomSearchBar: React.FC = () => {
    const [searchText, setSearchText] = React.useState('');

    return (
        <View

            style={styles.container}
        >
            <View style={styles.searchBox}>
                <TextInput
                    value={searchText}
                    onChangeText={setSearchText}
                    placeholder="Search"
                    style={styles.input}
                    placeholderTextColor='#8F8F8F'
                    multiline={false}
                    scrollEnabled={false}
                />

            </View>
            <View style={styles.searchIcon}>
                <Image source={SEARCH_ICON} style={styles.search} />
            </View>
        </View>
    );
};

export default CustomSearchBar;

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10,
        backgroundColor: COLORS.white,
        padding: wp(4),
        borderRadius: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 4,
        marginVertical: hp(1.7),
    },
    searchBox: {

        flex: 1,
        height: 37,
        alignItems: 'flex-start',
        backgroundColor: COLORS.white,
        borderRadius: 5,
        paddingHorizontal: wp(3),
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    input: {
        flex: 1,
        fontSize: 16,
        paddingVertical: hp(1.25),
        color: COLORS.searchText,
    },

    searchIcon: {
        width: wp(9),
        height: 35,
        resizeMode: 'center',
        padding: wp(1),
        backgroundColor: COLORS.white,
        elevation: 4,
        borderColor: COLORS.white,
        borderRadius: 5
    },
    search: {
        width: 25,
        height: 25,
        resizeMode: 'center'
    }
});
