import {call, put} from 'redux-saga/effects';
import {
  fetchSettingsRequest,
  fetchSettingsSuccess,
  fetchSettingsFailure,
} from './settingsSlice';
import {apiClient} from '../../../../services/httpServices';
import {API_URL} from '../../../../services/webConstants';

export function* fetchSettingsSaga() {
  yield put(fetchSettingsRequest());

  try {
    const {data, ok} = yield call(apiClient.get, API_URL.SETTINGS_DATA);
    console.log('Settings data from saga ===>', data);
    if (ok && data) {
      yield put(fetchSettingsSuccess(data.data));
    } else {
      yield put(fetchSettingsFailure('Failed to fetch settings'));
    }
  } catch (error) {
    yield put(
      fetchSettingsFailure(
        error.message || 'An error occurred while fetching settings',
      ),
    );
  }
}
