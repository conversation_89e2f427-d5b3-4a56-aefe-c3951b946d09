import {call, put} from 'redux-saga/effects';
import {
  fetchBusinessesRequest,
  fetchBusinessesSuccess,
  fetchBusinessesFailure,
  fetchBusinessDetailsRequest,
  fetchBusinessDetailsSuccess,
  fetchBusinessDetailsFailure,
} from './businessSlice';
import {apiClient} from '../../../../services/httpServices';
import {API_URL} from '../../../../services/webConstants';

// fetch the list of businesses
export function* fetchBusinessesSaga(action) {
  const {
    searchQuery = '',
    order_by = 'asc',
    category = '',
    page = 1,
  } = action.payload || {};
  yield put(fetchBusinessesRequest({page}));

  try {
    // const {data, ok} = yield call(apiClient.get, API_URL.BUSINESS_LIST);

    // test implementation of business search
    // const {
    //   searchQuery = '',
    //   order_by = 'asc',
    //   category = '',
    //   page = 1,
    // } = action.payload || {};
    const {data, ok} = yield call(
      apiClient.get,
      `${API_URL.BUSINESS_LIST}?search=${encodeURIComponent(
        searchQuery,
      )}&page=${page}&order_by=${encodeURIComponent(
        order_by,
      )}&category=${encodeURIComponent(category)}`,
    );

    console.log('data response', data);

    if (ok && data) {
      // yield put(fetchBusinessesSuccess(data.data));

      yield put(
        fetchBusinessesSuccess({
          data: data.data.data,
          total: data.data.total || 0,
          page,
        }),
      );
    } else {
      yield put(fetchBusinessesFailure('Failed to fetch businesses'));
    }
  } catch (error) {
    yield put(
      fetchBusinessesFailure(
        error.message || 'An error occurred while fetching businesses',
      ),
    );
  }
}

// fetch business details by ID
export function* fetchBusinessDetailsSaga(action) {
  console.log('inside business details saga');
  const {payload} = action;
  yield put(fetchBusinessDetailsRequest());

  try {
    const {data, ok} = yield call(
      apiClient.get,
      `${API_URL.BUSINESS_DETAILS}/${payload.id}`,
    );

    console.log('inside business details saga before ', data);

    if (ok && data) {
      console.log('inside business details saga', data);
      yield put(fetchBusinessDetailsSuccess({businessDetails: data.data}));
    } else {
      yield put(
        fetchBusinessDetailsFailure('Failed to fetch business details'),
      );
    }
  } catch (error) {
    yield put(
      fetchBusinessDetailsFailure(
        error.message || 'An error occurred while fetching business details',
      ),
    );
  }
}
