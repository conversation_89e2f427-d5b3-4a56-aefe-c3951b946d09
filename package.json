{"name": "PunjabiSamaj", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "build": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/ && rm -rf ./android/app/src/main/res/drawable-mdpi/ && rm -rf ./android/app/src/main/res/raw/", "release": "yarn build && cd ./android && ./gradlew bundleRelease", "postinstall": "patch-package"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.0", "@react-native-community/checkbox": "^0.5.16", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-firebase/app": "20.5.0", "@react-native-firebase/auth": "20.5.0", "@react-native-firebase/messaging": "20.5.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@reduxjs/toolkit": "^2.3.0", "apisauce": "^3.1.0", "axios": "^1.7.9", "he": "^1.2.0", "moment": "^2.30.1", "npm": "^10.9.0", "react": "18.2.0", "react-hook-form": "^7.53.2", "react-native": "0.73.0", "react-native-country-picker-modal": "^2.0.0", "react-native-dropdown-picker": "^5.4.6", "react-native-element-dropdown": "^2.12.2", "react-native-flash-message": "^0.4.2", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "2.14.0", "react-native-image-picker": "^7.1.2", "react-native-linear-gradient": "^2.8.3", "react-native-modal-datetime-picker": "^18.0.0", "react-native-otp-textinput": "^1.1.3", "react-native-pager-view": "^6.6.1", "react-native-permissions": "^5.2.0", "react-native-phone-number-input": "^2.1.0", "react-native-render-html": "^6.3.4", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "4.8.0", "react-native-screens": "^3.29.0", "react-native-snap-carousel": "^3.9.1", "react-native-splash-screen": "^3.3.0", "react-native-swiper-flatlist": "^3.2.5", "react-native-vector-icons": "^10.2.0", "react-native-version-check": "^3.4.7", "react-redux": "^9.1.2", "redux-persist": "^6.0.0", "redux-saga": "^1.3.0", "zod": "^3.24.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "^0.73.18", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "patch-package": "^8.0.0", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "main": "index.js", "author": "chiranjivDev <<EMAIL>>", "license": "MIT"}