import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  isLoading: false,
  isLoggingIn: false,
  token: '',
  userDetails: {},

  notificationId: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    userProfilePostStarted(state) {
      state.isLoading = true;
      state.isLoggingIn = false;
    },
    userProfilePostSuccess(state) {
      state.isLoading = false;
      state.isLoggingIn = true;
      // state.token = payload.token;
    },
    userProfilePostFail(state) {
      state.isLoading = false;
      state.isLoggingIn = false;
    },

    // login State

    userLoginStarted(state) {
      state.isLoggingIn = true;
    },
    userLoginSuccess(state, {payload}) {
      state.isLoggingIn = false;
      state.token = payload.token;
      state.userDetails = payload.user_details;
    },
    userLoginFail(state) {
      state.isLoggingIn = false;
    },
    userLogoutSuccess(state) {
      state.isLoggingIn = false;
      state.token = '';
      state.userDetails = '';
    },

    // set notification id
    setNotificationId(state, action) {
      state.notificationId = action.payload;
    },

    userDeleteSuccess(state) {
      state.isLoggingIn = false;
      state.token = '';
      state.userDetails = '';
    },
  },
});

const {actions, reducer} = authSlice;

export const {
  userProfilePostStarted,
  userProfilePostSuccess,
  userProfilePostFail,
  userLoginStarted,
  userLoginSuccess,
  userLoginFail,
  userLogoutSuccess,
  setNotificationId,

  userDeleteSuccess,
} = actions;
export default reducer;
