import React from 'react';
import {View, ScrollView, StyleSheet, Text, Image} from 'react-native';
import RenderHTML from 'react-native-render-html';
import {useWindowDimensions} from 'react-native';
import {useSelector} from 'react-redux';
import CustomHeader from '../../components/molecules/CustomHeader';
import COLORS from '../../themes/colors';
import {PUNJABI_ICON} from '../../utils/image-constants';
import he from 'he';

const ContactUsScreen = () => {
  const {width} = useWindowDimensions();
  // Fetch settings data from Redux
  const {settings, loading, error} = useSelector(state => state.settings);
  console.log('settings from contact us screen', settings?.contact_us);

  const contactUsHTML = settings?.contact_us || {};
  // Decode the HTML entities (like &lt;, &gt;, etc.)
  const decodedHTML = he.decode(contactUsHTML);

  const source = {
    html: decodedHTML,
  };

  if (loading) {
    return (
      <View>
        <Text>Loading...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View>
        <Text>{error}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <CustomHeader title="Contact Us" showBackButton={true} />

      {/* Logo */}
      <View style={styles.logoContainer}>
        <Image source={PUNJABI_ICON} style={styles.brandLogo} />
      </View>

      {/* Main Content */}
      <ScrollView
        contentContainerStyle={styles.contentWrapper}
        showsVerticalScrollIndicator={false}>
        <View style={styles.contentSection}>
          <Text style={styles.title}>Contact Us</Text>

          {/* Privacy Policy Content */}
          <View style={styles.section}>
            <RenderHTML contentWidth={width} source={source} />
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: 40,
    backgroundColor: COLORS.secondary,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 40,
  },
  brandLogo: {
    width: 150,
    height: 120,
    resizeMode: 'contain',
  },
  contentWrapper: {
    flexGrow: 1,
    paddingHorizontal: 20,
  },
  contentSection: {
    marginTop: 20,
  },
  title: {
    fontSize: 28,
    color: '#333',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  section: {
    marginBottom: 20,
  },
  subheading: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginTop: 10,
  },
  sectionText: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    textAlign: 'justify',
    marginBottom: 10,
  },
});

export default ContactUsScreen;
