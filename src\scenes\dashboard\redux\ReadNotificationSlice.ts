import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    isLoading: false,
    readNotification: {},

};

const ReadNotificationSlice = createSlice({
    name: 'readNotification',
    initialState,
    reducers: {
        readNotificationStarted: state => {
            state.isLoading = true;
        },

        readNotificationSuccess: (state, { payload }) => {

            state.isLoading = false;
            state.readNotification = payload.data;
            state.isLoading = true
        },

        readNotificationFailed: state => {
            state.isLoading = false;

        },
    },
});

export const { readNotificationStarted, readNotificationSuccess, readNotificationFailed } =
    ReadNotificationSlice.actions;
export default ReadNotificationSlice.reducer;