import React, {useEffect, useState} from 'react';
import {FlatList, StyleSheet, Text, View} from 'react-native';
import CustomHeader from '../../components/molecules/CustomHeader';
import {useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {ALL_NOTIFICATIONS} from './redux/NotficationAction';
import COLORS from '../../themes/colors';
import ROUTES from '../../navigation/RouteConstants';
import {READ_NOTIFICATION} from './redux/ReadNotificationAction';
import {INCREASE_COUNT} from './redux/IncreaseCountAction';
import NotificationsCard from '../../components/molecules/NotificationsCard';

const Notifications = () => {
  const notificationsData = useSelector(
    (state: any) => state.notification.notifications,
  );
  const navigation = useNavigation();

  const dispatch = useDispatch();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    dispatch({type: ALL_NOTIFICATIONS});
    dispatch({
      type: INCREASE_COUNT,
      payload: {
        alert_count: 0,
      },
    });
  }, []);

  const handleNavigateNotificationDetails = (item: any) => {
    navigation.navigate(ROUTES.NOTIFICATION_DETAILS, {notificationId: item.id});
  };

  const readNotification = (notificationId: any) => {
    const payload = {
      id: notificationId,
      is_read: 1,
    };
    dispatch({type: READ_NOTIFICATION, payload});
    dispatch({type: ALL_NOTIFICATIONS});
  };

  const renderNotificationCard = ({item}: {item: any}) => {
    return (
      <NotificationsCard
        item={item}
        onPress={handleNavigateNotificationDetails}
        onMarkRead={readNotification}
      />
    );
  };

  const handleRefresh = () => {
    setRefreshing(true);
    dispatch({type: ALL_NOTIFICATIONS}); // Refresh notifications
    setRefreshing(false); // Reset refreshing state once data is updated
  };
  return (
    <View style={styles.mainContainer}>
      <CustomHeader title="Notifications" showBackButton={true} />

      {notificationsData.length > 0 ? (
        <FlatList
          data={notificationsData}
          renderItem={renderNotificationCard}
          keyExtractor={item => item.id.toString()}
          refreshing={refreshing}
          onRefresh={handleRefresh}
        />
      ) : (
        <Text style={styles.emptyText}>
          You haven’t received any notifications yet. Stay tuned for updates on
          your events and community activities!
        </Text>
      )}
    </View>
  );
};

export default Notifications;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
    color: COLORS.black,
    marginHorizontal: 30,
  },
});
