import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    isLoading: false,
    notifications: [],




};

const notificationSlice = createSlice({
    name: 'notification',
    initialState,
    reducers: {
        allNotificationsStarted(state) {
            state.isLoading = true;
        },
        allNotificationsSuccess(state, { payload }) {
            state.isLoading = false;
            state.notifications = payload.data;
        },
        allNotificationsFail(state) {
            state.isLoading = false;
        },



    },
});

const { actions, reducer } = notificationSlice;

export const {
    allNotificationsStarted,
    allNotificationsSuccess,
    allNotificationsFail,
} = actions;
export default reducer;