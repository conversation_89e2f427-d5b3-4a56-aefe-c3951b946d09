import React from 'react';
import {
    StyleSheet,
    Text,
    View,
    TouchableOpacity,
    Image,
    Pressable,
    Alert,
    Share,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { BACK_ICON } from '../../utils/image-constants';
import { useSelector } from 'react-redux';
import COLORS from '../../themes/colors';
import SIcon from 'react-native-vector-icons/MaterialIcons';
import ROUTES from '../../navigation/RouteConstants';
import Icon from 'react-native-vector-icons/Entypo';
type GuestHeaderProps = {
    title: string;
    showBackButton?: boolean;
    userName?: string;
};

const GuestHeader: React.FC<GuestHeaderProps> = ({
    title,
    showBackButton = true,
    userName,
}) => {
    const navigation = useNavigation();
    const route = useRoute();

    const userDetails = useSelector((state: any) => state.auth.userDetails);
    const token = useSelector((state: any) => state.auth.token);
    console.log('user details', userDetails);

    const handleNavigateLogin = () => {
        navigation.navigate(ROUTES.LOGIN)
    }


    const appLink = 'https://play.google.com/store/apps/details?id=com.punjabisamaj';

    const onShare = async () => {
        try {
            const result = await Share.share({
                message: `Punjabi Samaj app: ${appLink}`,
            });
            if (result.action === Share.sharedAction) {
                if (result.activityType) {
                }
            } else if (result.action === Share.dismissedAction) {
            }
        } catch (error) {
            Alert.alert('Error', error.message);
        }
    };
    return (
        <View style={styles.container}>
            <View style={styles.headerText}>
                <Text style={styles.hello}>HELLO</Text>
                <Text style={styles.guest}>Guest</Text>
            </View>
            <View style={styles.rightSide}>
                <Pressable onPress={onShare} style={styles.login}>
                    <Text style={styles.loginText}>Share</Text>
                    <Icon
                        name="forward"
                        size={22}
                        color={COLORS.primary}
                    />
                </Pressable>
                <Pressable style={styles.login} onPress={handleNavigateLogin}>

                    <Text style={styles.loginText}>Login</Text>
                    <SIcon
                        name="arrow-forward"
                        size={22}
                        color={COLORS.primary}

                    />
                </Pressable>



            </View>
        </View>


    );
};

export default GuestHeader;

const styles = StyleSheet.create({
    container: {
        paddingTop: 25,
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
        paddingVertical: 10,
        paddingHorizontal: 12,
        backgroundColor: COLORS.primary,
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        elevation: 3,
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 1,
        // position: 'relative',
        height: 250,
    },
    headerText: {

    },
    rightSide: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        gap: 20
    },
    dashboardHeader: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
    },

    login: {
        borderWidth: 1,
        borderColor: COLORS.primary,
        backgroundColor: COLORS.secondary,
        paddingLeft: 15,
        paddingRight: 10,
        paddingVertical: 5,
        borderRadius: 20,
        height: 38,
        flexDirection: 'row',
        gap: 20,
        justifyContent: 'space-between',
        alignItems: 'center',


    },
    loginText: {
        color: COLORS.primary,

    },
    hello: {
        fontSize: 18,
        color: COLORS.white
    },
    guest: {
        fontSize: 28,
        fontWeight: '600',
        flex: 1,
        color: COLORS.white
    },
    titleContainer: {
        flex: 1,
    },
    title: {
        fontSize: 20,
        fontWeight: '600',
        color: COLORS.black,
        alignSelf: 'center',
    },
    backIcon: {
        marginVertical: 10,
    },

});
