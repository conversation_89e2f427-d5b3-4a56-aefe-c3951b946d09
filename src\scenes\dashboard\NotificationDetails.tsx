import React, {useEffect} from 'react';
import {
  Image,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {useRoute} from '@react-navigation/native';
import CustomHeader from '../../components/molecules/CustomHeader';
import COLORS from '../../themes/colors';
import {formatNotificationDate, hp, wp} from '../../utils/helper';
import {NOTIFICATION_DETAILS} from './redux/NotificationDetailAction';
import {clearNotificationDetails} from './redux/NotificationDetailSlice';
import ROUTES from '../../navigation/RouteConstants';

export const NotificationDetails = ({navigation}) => {
  const dispatch = useDispatch();
  const route = useRoute();
  const {notificationId} = route.params;

  const notificationDetailData = useSelector(
    state => state.notificationDetail?.notificationDetail,
  );

  useEffect(() => {
    if (notificationId) {
      const payload = {id: notificationId};
      dispatch({type: NOTIFICATION_DETAILS, payload});
      return () => {
        dispatch(clearNotificationDetails());
      };
    }
  }, [dispatch, notificationId]);

  const {
    id,
    title,
    message,
    image,
    event_location,
    event_date,
    event_time,
    notification_sent_date,
    notification_sent_time,
  } = notificationDetailData || {};

  const formattedDate = formatNotificationDate(
    notification_sent_date,
    notification_sent_time,
  );

  return (
    <View style={styles.mainContainer}>
      <CustomHeader title="Notification Details" showBackButton={true} />
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.imageContainer}>
          <Pressable
            onPress={() => {
              navigation.navigate(ROUTES.CAROUSEL_IMAGE_VIEW, {
                id: id,
                carouselImages: [
                  {
                    id: id,
                    image_url: image,
                  },
                ],
              });
            }}>
            <Image source={{uri: image}} style={styles.profileImg} />
          </Pressable>
          <View style={styles.nameBackdrop}>
            <Text style={styles.nameText}>{title || 'No Title Available'}</Text>
          </View>
        </View>

        <View style={styles.personalInfoSection}>
          <Text style={styles.sectionTitle}>Notification Details</Text>
          <View style={styles.field}>
            <Text style={styles.label}>Message</Text>
            <Text style={styles.value}>
              {message || 'No description Available'}
            </Text>
          </View>

          {event_location && (
            <View style={styles.field}>
              <Text style={styles.label}>Event Location</Text>
              <Text style={styles.value}>
                {event_location || 'Location not available'}
              </Text>
            </View>
          )}

          {event_date && (
            <View style={styles.field}>
              <Text style={styles.label}>Event Date</Text>
              <Text style={styles.value}>
                {event_date || 'Date not available'}
              </Text>
            </View>
          )}

          {event_time && (
            <View style={styles.field}>
              <Text style={styles.label}>Event Time</Text>
              <Text style={styles.value}>
                {event_time || 'Time not available'}
              </Text>
            </View>
          )}

          <View style={styles.field}>
            <Text style={styles.label}>Notification Sent Date</Text>
            <Text style={styles.value}>
              {formattedDate || 'Date/Time not available'}
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  scrollContainer: {
    paddingBottom: hp(2),
  },
  // imageContainer: {
  //   marginTop: hp(1),
  //   backgroundColor: COLORS.black,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  //   position: 'relative',
  // },
  // profileImg: {
  //   width: wp(70),
  //   height: hp(40),
  //   resizeMode: 'cover',
  // },
  imageContainer: {
    marginTop: hp(1),
    backgroundColor: COLORS.black,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  profileImg: {
    width: '100%',
    aspectRatio: 1.2,
    resizeMode: 'cover',
  },
  nameBackdrop: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    height: hp(8),
    width: '100%',
    bottom: 0,
    justifyContent: 'center',
  },
  nameText: {
    fontSize: wp(5),
    fontWeight: '600',
    color: COLORS.white,
    paddingHorizontal: wp(5),
  },
  personalInfoSection: {
    paddingHorizontal: wp(5),
    marginTop: hp(2),
  },
  sectionTitle: {
    fontSize: wp(5),
    fontWeight: '600',
    color: COLORS.black,
    marginBottom: hp(2),
  },
  field: {
    marginBottom: hp(2),
  },
  label: {
    fontSize: wp(4),
    fontWeight: '500',
    color: COLORS.black,
  },
  value: {
    fontSize: wp(4),
    color: COLORS.black,
    marginTop: hp(0.5),
  },
});
