import {Pressable, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {useNavigation} from '@react-navigation/native';
import Icon from 'react-native-vector-icons/FontAwesome';
import COLORS from '../../themes/colors';
import ROUTES from '../../navigation/RouteConstants';

const NotificationCard = ({notificationCount}: any) => {
  const navigation = useNavigation();

  const handleNotifications = () => {
    navigation.navigate(ROUTES.NOTIFICATIONS);
  };

  return (
    <Pressable style={styles.cardContainer} onPress={handleNotifications}>
      {/* Notification Icon */}
      <View style={styles.iconContainer}>
        <Icon name="bell" size={22} color={COLORS.primary} />
      </View>

      {/* Text and Notification Count */}
      <View style={styles.textContainer}>
        <Text style={styles.notificationText}>Notifications</Text>
        <View
          style={[
            styles.notificationCount,
            notificationCount > 0 ? styles.notificationCountWithBg : {},
          ]}>
          {notificationCount > 0 ? (
            <Text style={styles.countText}>{notificationCount}</Text>
          ) : (
            <Icon name="angle-right" size={22} color={COLORS.primary} />
          )}
        </View>
      </View>
    </Pressable>
  );
};

export default NotificationCard;

const styles = StyleSheet.create({
  cardContainer: {
    height: 65,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingHorizontal: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginLeft: 20,
    flex: 1,
  },
  notificationText: {
    fontSize: 20,
    color: 'white',
    fontWeight: '600',
    flex: 1,
  },
  notificationCount: {
    width: 25,
    height: 25,
    borderRadius: 12.5,
    // backgroundColor: 'transparent',
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  notificationCountWithBg: {
    backgroundColor: 'white',
  },
  countText: {
    fontSize: 14,
    color: '#E55E2A',
    fontWeight: 'bold',
  },
});
