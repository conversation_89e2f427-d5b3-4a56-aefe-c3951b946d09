import {create} from 'apisauce';
import {store} from '../store/store';

export const apiClient = create({
  baseURL: 'https://punjabisamaj.phpteam.in/public/api/',
});

apiClient.addRequestTransform(request => {
  const {
    auth: {token},
  } = store.getState();
  // Debug: Log the token value
  console.log('Token from Redux store:', token);
  request.headers['Authorization'] = `Bearer ${token}`;
});
