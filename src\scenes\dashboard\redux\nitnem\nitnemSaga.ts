import {call, put} from 'redux-saga/effects';
import {
  fetchNitnemListRequest,
  fetchNitnemListSuccess,
  fetchNitnemListFailure,
} from './nitnemSlice';
import {apiClient} from '../../../../services/httpServices';
import {API_URL} from '../../../../services/webConstants';

export function* fetchNitnemSaga() {
  console.log('Inside nitnem saga');
  yield put(fetchNitnemListRequest());

  try {
    const {data, ok} = yield call(apiClient.get, API_URL.NITNEM_LIST);
    console.log('Inside nitnem saga data', data);

    if (ok && data) {
      yield put(fetchNitnemListSuccess(data.data));
    } else {
      yield put(fetchNitnemListFailure('Failed to fetch Nitnem list'));
    }
  } catch (error) {
    yield put(
      fetchNitnemListFailure(
        error.message || 'An error occurred while fetching the Nitnem list',
      ),
    );
  }
}
