import React, {useEffect} from 'react';
import {Image, StyleSheet, View, Dimensions, Pressable} from 'react-native';
import CustomHeader from '../../components/molecules/CustomHeader';
import {ScrollView} from 'react-native-gesture-handler';
import NotificationCard from '../../components/molecules/NotificationCard';
import {useNavigation} from '@react-navigation/native';
import COLORS from '../../themes/colors';
import {useDispatch, useSelector} from 'react-redux';
import ROUTES from '../../navigation/RouteConstants';
import {setNotificationId} from '../auth/redux/AuthSlice';
import {NOTIFICATIONS_COUNT} from './redux/NotificationCountAction';
import {selectNotificationCount} from '../../store/selectors';
import DirectivesSection from '../../components/molecules/Directives';
import SwiperFlatList from 'react-native-swiper-flatlist';
import {FETCH_BANNER_DATA} from './redux/banner/bannerAction';
import Nitnem from './Nitnem';
import {FETCH_NITNEM} from './redux/nitnem/nitnemAction';

const {width} = Dimensions.get('window');

const Dashboard = () => {
  const {notificationId} = useSelector((state: any) => state.auth);
  const {banners} = useSelector((state: any) => state.banners);

  // console.log('banners data from UI', banners);

  const notificationCount = useSelector(selectNotificationCount);
  const navigation = useNavigation();

  const dispatch = useDispatch();

  // Navigate to notification details in case we get the ID from the notification data
  useEffect(() => {
    if (notificationId) {
      navigation.navigate(ROUTES.NOTIFICATION_DETAILS, {
        notificationId: notificationId,
      });
      dispatch(setNotificationId(null));
    }
  }, [notificationId, navigation]);

  useEffect(() => {
    dispatch({type: NOTIFICATIONS_COUNT});
  }, []);

  const handleNavigatePunjabiSamaj = () => {
    navigation.navigate(ROUTES.PUNJABI_SAMAJ);
  };

  const handleNavigateBusinessList = () => {
    navigation.navigate(ROUTES.BUSINESS_LIST);
  };

  // fetch Banner data
  useEffect(() => {
    dispatch({type: FETCH_BANNER_DATA});
    // fetch Nitnem
    dispatch({type: FETCH_NITNEM});
  }, []);

  return (
    <View style={styles.container}>
      <CustomHeader title="Dashboard" showBackButton={false} />
      <ScrollView style={styles.belowText}>
        <View style={styles.notificationOuter}>
          <NotificationCard notificationCount={notificationCount} />
        </View>
        {/* SwiperFlatList for Carousel*/}
        {!banners.every(obj => obj.status === '0') && (
          <View style={styles.carouselContainer}>
            <SwiperFlatList
              data={banners.filter(obj => obj.status === '1')}
              renderItem={({item}) => (
                <Pressable
                  style={styles.carouselItem}
                  onPress={() => {
                    navigation.navigate(ROUTES.CAROUSEL_IMAGE_VIEW, {
                      id: item.id,
                      carouselImages: banners,
                    });
                  }}>
                  <Image source={{uri: item?.image_url}} style={styles.image} />
                </Pressable>
              )}
              keyExtractor={item => item.id}
              paginationStyleItemActive={styles.paginationItemActive}
              paginationStyleItemInactive={styles.paginationItemInactive}
              paginationStyle={styles.paginationStyle}
              autoplay
              autoplayLoop
              autoplayDelay={3}
              style={styles.carousel}
              showPagination
              autoplayLoopKeepAnimation
            />
          </View>
        )}
        {/* Nitnem Section*/}
        <Nitnem />
        {/* Directives Section */}
        <DirectivesSection
          handleNavigatePunjabiSamaj={handleNavigatePunjabiSamaj}
          handleNavigateBusinessList={handleNavigateBusinessList}
        />
      </ScrollView>
    </View>
  );
};

export default Dashboard;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  belowText: {
    paddingHorizontal: 10,
  },
  notificationOuter: {
    marginTop: 10,
  },

  // Carousel
  carouselContainer: {
    // alignItems: 'center',
    // borderRadius: 10,

    // marginVertical: 20,
    borderRadius: 10,
    overflow: 'hidden',
    backgroundColor: COLORS.white,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
  },

  carousel: {
    width: width,
    height: 300,
  },
  carouselItem: {
    width: width,
    backgroundColor: COLORS.white,
    overflow: 'hidden',
  },
  image: {
    width: width,
    height: '100%',
    resizeMode: 'cover',
  },

  paginationItemActive: {
    backgroundColor: COLORS.primary,
    width: 12,
    height: 12,
    borderRadius: 6,
    marginHorizontal: 5,
  },
  paginationItemInactive: {
    backgroundColor: 'white',
    width: 10,
    height: 10,
    marginHorizontal: 4,
  },

  paginationStyle: {
    bottom: 0,
    zIndex: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
