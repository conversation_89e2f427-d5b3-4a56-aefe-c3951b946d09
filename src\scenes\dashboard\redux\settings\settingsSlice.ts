import {createSlice} from '@reduxjs/toolkit';

const settingsSlice = createSlice({
  name: 'settings',
  initialState: {
    settings: {},
    loading: false,
    error: null,
  },
  reducers: {
    fetchSettingsRequest(state) {
      state.loading = true;
      state.error = null;
    },
    fetchSettingsSuccess(state, action) {
      state.loading = false;
      state.settings = action.payload;
    },
    fetchSettingsFailure(state, action) {
      state.loading = false;
      state.error = action.payload;
    },
  },
});

export const {
  fetchSettingsRequest,
  fetchSettingsSuccess,
  fetchSettingsFailure,
} = settingsSlice.actions;

export default settingsSlice.reducer;
