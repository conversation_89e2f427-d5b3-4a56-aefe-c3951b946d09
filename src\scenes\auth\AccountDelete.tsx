import React from 'react';
import {View, Text, StyleSheet, ScrollView, Image, Alert} from 'react-native';
import {PUNJABI_ICON} from '../../utils/image-constants';
import COLORS from '../../themes/colors';
import {useNavigation} from '@react-navigation/native';
import ROUTES from '../../navigation/RouteConstants';
import {useDispatch, useSelector} from 'react-redux';
import axios from 'axios';
import {showMessage} from 'react-native-flash-message';
import {API_URL} from '../../services/webConstants';
import {userDeleteSuccess} from './redux/AuthSlice';
import CustomHeader from '../../components/molecules/CustomHeader';

const AccountDeleteScreen = () => {
  const navigation = useNavigation();
  const userDetails = useSelector((state: any) => state.auth.userDetails);
  const token = useSelector((state: any) => state.auth.token);

  const dispatch = useDispatch();

  // handle delete account
  const handleDeleteAccount = async () => {
    if (!userDetails?.id) {
      showMessage({
        message: 'Error',
        description: 'User ID not found.',
        type: 'danger',
      });
      return;
    }

    // Show confirmation alert
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await axios.delete(
                `https://punjabisamaj.phpteam.in/public/api/${API_URL.DELETE_USER_BY_ID}/${userDetails.id}`,
                {
                  headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                  },
                },
              );
              console.log('response', response);

              if (response.status === 200 && response.data?.success) {
                showMessage({
                  message: 'Account Deleted',
                  description: 'Your account has been deleted successfully.',
                  type: 'success',
                });
                dispatch(userDeleteSuccess());
              } else {
                showMessage({
                  message: 'Error',
                  description: 'Failed to delete account. Please try again.',
                  type: 'danger',
                });
              }
            } catch (error) {
              console.error('Error deleting account:', error);
              showMessage({
                message: 'Error',
                description: 'An unexpected error occurred. Please try again.',
                type: 'danger',
              });
            }
          },
        },
      ],
      {cancelable: true},
    );
  };

  // handle keep account
  const handleKeepAccount = async () => {
    navigation.navigate(ROUTES.DASHBOARD);
  };

  return (
    <View style={styles.container}>
      <CustomHeader title="Account Delete" showBackButton={true} />

      {/* Logo */}
      <View style={styles.logoContainer}>
        <Image source={PUNJABI_ICON} style={styles.brandLogo} />
      </View>

      {/* Main Content */}
      <ScrollView
        contentContainerStyle={styles.contentWrapper}
        showsVerticalScrollIndicator={false}>
        <View style={styles.contentSection}>
          <Text style={styles.title}>Request Account Deletion</Text>

          {/* Information Section */}
          <View style={styles.section}>
            <Text style={styles.sectionText}>
              We're sorry to see you go. Deleting your account means losing
              access to your saved content, preferences, and community updates.
            </Text>
            <Text style={styles.sectionText}>
              You'll miss future events, news, and notifications.
            </Text>
          </View>

          {/* Buttons */}
          <View style={{marginVertical: 20}}>
            <View style={styles.buttonContainer}>
              <Text
                style={[styles.buttonText, styles.keepButton]}
                onPress={handleKeepAccount}>
                Keep My Account
              </Text>
            </View>
            <View style={styles.buttonContainer}>
              <Text
                style={[styles.buttonText, styles.deleteButton]}
                onPress={handleDeleteAccount}>
                Delete Account
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default AccountDeleteScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: 40,
    backgroundColor: COLORS.secondary,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 40,
  },
  brandLogo: {
    width: 150,
    height: 120,
    resizeMode: 'contain',
  },
  contentWrapper: {
    flexGrow: 1,
    paddingHorizontal: 20,
  },
  contentSection: {
    marginTop: 20,
  },
  title: {
    fontSize: 28,
    color: '#333',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  section: {},
  sectionText: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 10,
    paddingHorizontal: 20,
  },
  buttonContainer: {
    marginVertical: 8,
    marginHorizontal: 10,
  },
  buttonText: {
    fontSize: 18,
    color: 'white',
    textAlign: 'center',
    paddingVertical: 15,
    borderRadius: 5,
    fontWeight: 'bold',
  },
  keepButton: {
    backgroundColor: '#4CAF50',
  },
  deleteButton: {
    backgroundColor: COLORS.primary,
  },
});
