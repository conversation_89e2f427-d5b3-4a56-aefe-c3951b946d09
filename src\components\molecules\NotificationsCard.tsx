import React from 'react';
import {Image, StyleSheet, Text, View, Pressable} from 'react-native';
import COLORS from '../../themes/colors';
import {formatNotificationDate, hp, wp} from '../../utils/helper';

const NotificationsCard = ({item, onPress, onMarkRead}) => {
  const isRead = item.is_read === '1';

  const formattedDate = formatNotificationDate(
    item.notification_sent_date,
    item.notification_sent_time,
  );

  return (
    <View style={styles.shadowWrapper}>
      <Pressable
        style={[styles.card, isRead ? styles.read : styles.unread]}
        onPress={() => {
          onPress(item);
          onMarkRead(item.id);
        }}>
        {/* Image Section */}
        <View style={styles.imageContainer}>
          <Image
            source={{uri: item.image}}
            style={[styles.image, isRead ? styles.readImage : null]}
          />
        </View>

        {/* Text Section */}
        <View style={styles.textSection}>
          <View style={styles.titleContainer}>
            <Text
              style={[
                styles.headingText,
                isRead ? styles.readTitle : styles.unreadTitle,
              ]}
              numberOfLines={1}>
              {item.title}
            </Text>
            {/* Dot for read/unread notifications */}
            <View style={[isRead ? styles.readDot : styles.unreadDot]} />
          </View>

          <Text
            style={[
              styles.messageText,
              isRead ? styles.readText : styles.unreadText,
            ]}
            numberOfLines={2}>
            {item.message}
          </Text>

          <Text
            style={[
              styles.messageText,
              isRead ? styles.readText : styles.unreadText,
            ]}>
            {formattedDate}
          </Text>
        </View>
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  shadowWrapper: {
    marginHorizontal: wp(4),
    marginVertical: hp(0.4),
    borderRadius: 10,
    overflow: 'hidden',
  },
  card: {
    flexDirection: 'row',
    borderRadius: 10,
    overflow: 'hidden',
    padding: wp(4),
    alignItems: 'flex-start',
    shadowColor: COLORS.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 5,
  },
  read: {
    backgroundColor: COLORS.lightGray,
  },
  unread: {
    backgroundColor: '#fcefe7',
  },
  imageContainer: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(7.5),
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: wp(4),
    backgroundColor: COLORS.white,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  readImage: {
    opacity: 0.5,
  },
  textSection: {
    flex: 1,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headingText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  messageText: {
    fontSize: 13,
    marginBottom: hp(0.5),
  },
  dateText: {
    fontSize: 12,
    color: COLORS.gray,
  },
  unreadText: {
    color: '#7a7a7a',
  },
  readText: {
    color: COLORS.black,
    opacity: 0.4,
  },
  unreadTitle: {
    color: COLORS.black,
  },
  readTitle: {
    opacity: 0.8,
  },
  unreadDot: {
    width: wp(1.7),
    height: wp(1.7),
    borderRadius: wp(1),
    backgroundColor: COLORS.primary,
    marginLeft: wp(2),
  },
  readDot: {
    width: wp(1.7),
    height: wp(1.7),
    borderRadius: wp(1),
    backgroundColor: '#d9d9d9',
    marginLeft: wp(2),
  },
});

export default NotificationsCard;
