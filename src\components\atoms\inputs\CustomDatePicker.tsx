import React, { useState } from 'react';
import { View, StyleSheet, Pressable, Text } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import COLORS from '../../../themes/colors';
import TextExtended from '../texts/AppText';

type CustomDatePickerProps = {
    label: string;
    placeholder: string;
    value: string | null;
    onChange: (date: string) => void;
};

const CustomDatePicker: React.FC<CustomDatePickerProps> = ({
    label,
    placeholder,
    value,
    onChange,
}) => {
    const [isFocused, setIsFocused] = useState(false);
    const [showPicker, setShowPicker] = useState(false);

    const handleFocus = () => setIsFocused(true);
    const handleBlur = () => setIsFocused(false);

    const handleDateChange = (event: any, date?: Date) => {
        setShowPicker(false);
        if (date) {
            const formattedDate = date.toISOString().split('T')[0];
            onChange(formattedDate);
        }
    };

    return (
        <View style={styles.container}>
            <Pressable
                onPress={() => setShowPicker(true)}
                onFocus={handleFocus}
                onBlur={handleBlur}

                style={[
                    styles.inputField,
                    isFocused || value ? styles.inputFieldActive : null,
                ]}>

                <Text style={styles.textValue}>{value || ''}</Text>

            </Pressable>

            {showPicker && (
                <DateTimePicker
                    value={value ? new Date(value) : new Date()}
                    mode="date"
                    display="default"
                    onChange={handleDateChange}
                />
            )}
        </View>
    );
};

export default CustomDatePicker;

const styles = StyleSheet.create({
    container: {
        width: '100%',
        flex: 1


    },
    inputField: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 7,
        padding: 10,

    },
    inputFieldActive: {

        borderColor: COLORS.primary,
        borderWidth: 1,
    },

    textValue: {
        fontSize: 16,
        color: COLORS.black,
        flex: 1,
    },

});
