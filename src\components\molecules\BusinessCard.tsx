import React from 'react';
import { View, Text, Pressable, Image, StyleSheet, Linking } from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import moment from 'moment'; // Import moment
import COLORS from '../../themes/colors';

const BusinessCard = ({ item, onPress }) => {
  console.log('business card item', item);
  // Function to calculate years in business using moment.js
  const calculateYearsInBusiness = business_established_year => {
    if (!business_established_year) return 'N/A';
    const currentDate = moment();
    const businessDate = moment(business_established_year);
    const yearsInBusiness = currentDate.diff(businessDate, 'years');
    return yearsInBusiness > 0
      ? `${yearsInBusiness} years in business`
      : 'Less than a year in business';
  };

  const handleWhatsApp = () => {
    const phoneNumber = item.business_whatsapp || '1234567890';
    const url = `https://wa.me/${phoneNumber}`;
    Linking.openURL(url).catch(err =>
      console.error('Error opening WhatsApp:', err),
    );
  };

  const handleCall = () => {
    const phoneNumber = `tel:${item.business_whatsapp || '1234567890'}`;
    Linking.openURL(phoneNumber).catch(err =>
      console.error('Error making call:', err),
    );
  };

  return (
    <View style={styles.shadowWrapper}>
      <Pressable style={styles.card} onPress={() => onPress(item.id)}>
        {/* Image Section */}
        <View style={styles.imageContainer}>
          <Image
            source={{
              uri: item.business_image || 'https://via.placeholder.com/150',
            }}
            style={styles.image}
          />
          {true && (
            <View style={styles.categoryBadge}>
              <Text style={styles.categoryText}>
                {item.category || 'Category'}
              </Text>
            </View>
          )}
        </View>

        {/* Text Section */}
        <View style={styles.textSection}>
          <Text style={styles.headingText}>
            {item.business_name || 'Business Name'}
          </Text>
          <Text style={styles.subText}>
            {item.business_info || 'This is a description of the business.'}
          </Text>

          {/* Address Section */}
          <Text style={styles.addressText}>
            {item.business_address || 'Business Address Not Available'}
          </Text>

          {/* Years in Business Section */}
          <Text style={styles.yearsInBusiness}>
            {calculateYearsInBusiness(item.business_established_year)}
          </Text>

          {/* Created At Section */}
          {/* <Text style={styles.date}>
            {item.created_at
              ? moment(item.created_at).format('MM/DD/YYYY') 
              : 'Date'}
          </Text> */}

          {/* View Details - Subtle Indicator */}
          <Pressable
            onPress={() => onPress(item.id)}
            style={styles.viewDetailsWrapper}>
            <Text style={styles.viewDetailsText}>View Details</Text>
            <Icon name="chevron-right" size={16} color={COLORS.primary} />
          </Pressable>
        </View>

        {/* Action Section */}
        <View style={styles.actionSection}>
          <Pressable onPress={handleCall} style={styles.iconWrapper}>
            <Icon name="phone" size={24} color="#007AFF" />
            <Text style={styles.actionText}>Call Now</Text>
          </Pressable>
          <Pressable onPress={handleWhatsApp} style={styles.iconWrapper}>
            <Icon name="whatsapp" size={24} color="#25D366" />
            <Text style={styles.actionText}>Chat</Text>
          </Pressable>
        </View>
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  shadowWrapper: {
    marginBottom: 16,
    marginHorizontal: 8,
  },
  card: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  // image: {
  //   width: '100%',
  //   height: 150,
  // },

  // test
  imageContainer: {
    position: 'relative',
    width: '100%',
    aspectRatio: 16 / 9, // Set container to maintain 16:9 aspect ratio
  },
  image: {
    width: '100%',
    height: '100%',
    // borderRadius: 8,
    resizeMode: 'cover', // Ensure the image covers the container
  },

  categoryBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: COLORS.primary,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  categoryText: {
    color: COLORS.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
  textSection: {
    padding: 16,
  },
  headingText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.black,
    marginBottom: 8,
  },
  subText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  addressText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  yearsInBusiness: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 8,
  },
  date: {
    fontSize: 12,
    color: COLORS.gray,
    marginTop: 8,
  },
  actionSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 8,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  iconWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  actionText: {
    fontSize: 12,
    color: COLORS.black,
    marginLeft: 8,
  },
  viewDetailsWrapper: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginTop: 5,
  },
  viewDetailsText: {
    fontSize: 14,
    color: COLORS.primary,
    marginRight: 8,
    fontWeight: 'bold',
  },
});

export default BusinessCard;
