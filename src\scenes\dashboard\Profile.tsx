import { Alert, Image, Pressable, Share, StyleSheet, Text, View } from 'react-native';
import React, { useEffect } from 'react';
import {
  ABOUT_ICON,
  CONDITION_ICON,
  CONTACT_ICON,
  LOGOUT_ICON,
  PENCIL_ICON,
  PRIVACY_ICON,
  SHARE_ICON,
} from '../../utils/image-constants';
import { useDispatch, useSelector } from 'react-redux';
import { userLogoutSuccess } from '../auth/redux/AuthSlice';
import COLORS from '../../themes/colors';
import auth from '@react-native-firebase/auth';
import ROUTES from '../../navigation/RouteConstants';
import { useNavigation } from '@react-navigation/native';
import CustomHeader from '../../components/molecules/CustomHeader';
import { ScrollView } from 'react-native-gesture-handler';
import Icon from 'react-native-vector-icons/FontAwesome';
import { FETCH_SETTINGS_DATA } from './redux/settings/settingsAction';
import IconS from 'react-native-vector-icons/Entypo';
const Profile = () => {
  const dispatch = useDispatch();
  const { userDetails, token } = useSelector((state: any) => state.auth);
  const { settings, isLoading } = useSelector((state: any) => state.settings);

  console.log('settings data from the profile screen', settings);

  const handleLogout = async () => {
    try {
      // first sign out the user from firebase
      await auth().signOut();
      dispatch(userLogoutSuccess());
    } catch (error) {
      console.error('Error signing out from Firebase:', error);
    }
  };

  const navigation = useNavigation();

  const handleDelete = async () => {
    navigation.navigate(ROUTES.DELETE_ACCOUNT);
  };

  // Common navigation method
  const handleNavigate = async screen => {
    navigation.navigate(screen);
  };

  // fetch profiles data
  useEffect(() => {
    dispatch({ type: FETCH_SETTINGS_DATA });
  }, []);


  const appLink = 'https://play.google.com/store/apps/details?id=com.punjabisamaj';

  const handleShareApp = async () => {
    try {
      const result = await Share.share({
        message: `Punjabi Samaj app: ${appLink}`,
      });
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
        }
      } else if (result.action === Share.dismissedAction) {
      }
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };
  return (
    <View style={styles.mainContainer}>
      <CustomHeader title="Settings" showBackButton={true} />

      {/* <View style={styles.bgContainer}></View> */}

      <ScrollView style={styles.bottomCard}>
        <View style={{ paddingHorizontal: 15, paddingTop: 20 }}>
          <View style={styles.profileCard}>
            <View style={styles.left}>
              <View style={styles.imageContainer}>
                <Image
                  source={{
                    uri: userDetails?.user_image || 'https://i.pravatar.cc/150',
                  }}
                  style={styles.profileIcon}
                />
              </View>
              <View>
                <Text style={styles.userName}>{userDetails?.name}</Text>
                <Text style={styles.email}>
                  {userDetails.email || 'Not Available'}
                </Text>
              </View>
            </View>

            {/* Edit Icon : comment out for now */}
            {/* <View style={styles.edit}>
              <Image source={PENCIL_ICON} style={styles.pencilIcon} />
            </View> */}
          </View>

          {/* End of profile card */}

          <View style={styles.menuSection}>
            <Pressable
              style={styles.menuItem}
              onPress={() => handleNavigate(ROUTES.ABOUT_US)}>
              <View style={styles.iconContainer}>
                <Image source={ABOUT_ICON} style={styles.icon} />
              </View>
              <Text style={styles.menuText}>About Us</Text>
            </Pressable>
            {/* Comment out for now */}
            {/* <Pressable style={styles.menuItem}>
              <View style={styles.iconContainer}>
                <Image source={SHARE_ICON} style={styles.icon} />
              </View>
              <Text style={styles.menuText}>Share App</Text>
            </Pressable> */}
            <Pressable
              style={styles.menuItem}
              onPress={() => handleNavigate(ROUTES.CONTACT_US)}>
              <View style={styles.iconContainer}>
                <Image source={CONTACT_ICON} style={styles.icon} />
              </View>
              <Text style={styles.menuText}>Contact Us</Text>
            </Pressable>
            <Pressable
              style={styles.menuItem}
              onPress={() => handleNavigate(ROUTES.TERMS_CONDITIONS)}>
              <View style={styles.iconContainer}>
                <Image source={CONDITION_ICON} style={styles.icon} />
              </View>
              <Text style={styles.menuText}>Terms & Conditions</Text>
            </Pressable>
            <Pressable
              style={styles.menuItem}
              onPress={() => handleNavigate(ROUTES.PRIVACY_POLICY)}>
              <View style={styles.iconContainer}>
                <Image source={PRIVACY_ICON} style={styles.iconP} />
              </View>
              <Text style={styles.menuText}>Privacy Policy </Text>
            </Pressable>
            <Pressable
              style={styles.menuItem}
              onPress={handleShareApp}>
              <View style={styles.iconContainer}>
                <IconS
                  name="forward"
                  size={28}
                  color={COLORS.white}
                />
              </View>
              <Text style={styles.menuText}> Share App </Text>
            </Pressable>
            <Pressable style={styles.menuItem} onPress={handleLogout}>
              <View style={styles.iconContainer}>
                <Image source={LOGOUT_ICON} style={styles.icon} />
              </View>
              <Text style={styles.menuText}>Log Out</Text>
            </Pressable>

            {/* Delete Account */}
            <Pressable style={styles.menuItem} onPress={handleDelete}>
              <View style={styles.iconContainer}>
                <Icon name="trash" size={24} color="#fff" />
              </View>
              <Text style={styles.deleteText}>Delete Account</Text>
            </Pressable>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default Profile;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  bgContainer: {
    width: '100%',
    height: 147,
    borderBottomLeftRadius: 15,
    borderBottomRightRadius: 15,
    backgroundColor: COLORS.white,
    position: 'relative',
  },
  bottomCard: {
    // position: 'absolute',
    // padding: 15,
    // top: 90,
    // width: '100%',
  },

  imageContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: COLORS.primary,
    overflow: 'hidden',
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileIcon: {
    width: '100%',
    height: '100%',
    borderRadius: 30,
    resizeMode: 'cover',
  },
  profileCard: {
    borderRadius: 15,
    backgroundColor: COLORS.white,
    paddingHorizontal: 15,
    paddingVertical: 25,
    flexDirection: 'row',
    gap: 15,
    alignItems: 'center',
    elevation: 5,
    justifyContent: 'space-between',
  },
  left: {
    flexDirection: 'row',
    gap: 15,
    alignItems: 'center',
  },
  userName: {
    color: COLORS.black,
    fontSize: 16,
    fontWeight: '600',
  },
  email: {
    fontSize: 12,
    fontWeight: '500',
  },
  edit: {
    borderWidth: 1,
    borderColor: '#F7CCBB',
    padding: 5,
    width: 30,
    height: 30,
    borderRadius: 5,
    resizeMode: 'center',
    marginEnd: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pencilIcon: {
    width: 20,
    height: 20,

    padding: 10,
  },
  menuSection: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingVertical: 15,
    gap: 15,
  },
  menuItem: {
    backgroundColor: COLORS.white,
    borderRadius: 5,
    paddingVertical: 15,
    paddingHorizontal: 10,
    width: '47%',
    alignItems: 'center',
    elevation: 8,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3.5,
  },
  iconContainer: {
    width: 46,
    height: 46,
    borderRadius: 23,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  icon: {
    resizeMode: 'contain',
    width: 30,
    height: 30,
  },
  iconP: {
    resizeMode: 'center',
    width: 27,
    height: 27,
    alignSelf: 'center',
    marginLeft: 3,
  },
  menuText: {
    fontSize: 16,
    color: COLORS.black,
    fontWeight: '500',
  },

  deleteText: {
    fontSize: 16,
    color: 'red',
    fontWeight: '500',
  },
});
