import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import LoginScreen from '../scenes/auth/Login';
import VerifyOTPScreen from '../scenes/auth/VerifyOTP';
import ROUTES from './RouteConstants';
import ProfileSetup from '../scenes/auth/ProfileSetup';
import Dashboard from '../scenes/dashboard/Dashboard';
import Notifications from '../scenes/dashboard/Notifications';
import Profile from '../scenes/dashboard/Profile';
import PunjabiSamaj from '../scenes/dashboard/PunjabiSamaj';
import ProfileDetailPage from '../scenes/dashboard/ProfileDetailPage';
import BusinessDetails from '../scenes/auth/BusinessDetails';

import { useSelector } from 'react-redux';
import BusinessList from '../scenes/dashboard/BusinessList';
import DetailsBusiness from '../scenes/dashboard/DetailsBusiness';
import { NotificationDetails } from '../scenes/dashboard/NotificationDetails';
import AccountDeleteScreen from '../scenes/auth/AccountDelete';
import PrivacyPolicyScreen from '../scenes/dashboard/PrivacyPolicyScreen';
import TermsAndConditionsScreen from '../scenes/dashboard/TermsAndConditions';
import ContactUsScreen from '../scenes/dashboard/ContactUs';
import AboutUsScreen from '../scenes/dashboard/AboutUs';
import CarouselImageView from '../scenes/CarouselImageView';
import Nitnem from '../scenes/dashboard/Nitnem';
import NitnemScreen from '../scenes/dashboard/NitnemScreen';
import NitnemDetails from '../scenes/dashboard/NitnemDetails';
import GuestDashboard from '../scenes/dashboard/GuestDashboard';

const Routes = () => {
  const Stack = createStackNavigator();
  const token = useSelector(state => state?.auth?.token);

  return (
    <Stack.Navigator>
      {!token ? (
        <Stack.Group>
          <Stack.Screen
            name={ROUTES.GUEST_DASHBOARD}
            component={GuestDashboard}
            options={{ headerShown: false }} />
          <Stack.Screen
            name={ROUTES.NITNEM}
            component={NitnemScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name={ROUTES.NITNEM_DETAILS}
            component={NitnemDetails}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name={ROUTES.LOGIN}
            component={LoginScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name={ROUTES.OTP_VERIFICATION}
            component={VerifyOTPScreen}
            options={{ headerShown: false }}
          />

          <Stack.Screen
            name={ROUTES.PROFILE_SETUP}
            component={ProfileSetup}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name={ROUTES.BUSINESS_DETAILS}
            component={BusinessDetails}
            options={{ headerShown: false }}
          />

        </Stack.Group>
      ) : (
        <Stack.Group>
          <Stack.Screen
            name={ROUTES.DASHBOARD}
            component={Dashboard}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name={ROUTES.NOTIFICATIONS}
            component={Notifications}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name={ROUTES.PROFILE}
            component={Profile}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name={ROUTES.PUNJABI_SAMAJ}
            component={PunjabiSamaj}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name={ROUTES.PROFILE_DETAIL}
            component={ProfileDetailPage}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name={ROUTES.NOTIFICATION_DETAILS}
            component={NotificationDetails}
            options={{ headerShown: false }}
            initialParams={{ notificationId: null }}
          />
          <Stack.Screen
            name={ROUTES.BUSINESS_LIST}
            component={BusinessList}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name={ROUTES.DETAILS_BUSINESS}
            component={DetailsBusiness}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name={ROUTES.DELETE_ACCOUNT}
            component={AccountDeleteScreen}
            options={{ headerShown: false }}
          />

          {/* privacy policy  */}
          <Stack.Screen
            name={ROUTES.PRIVACY_POLICY}
            component={PrivacyPolicyScreen}
            options={{ headerShown: false }}
          />

          {/* Terms and conditions */}
          <Stack.Screen
            name={ROUTES.TERMS_CONDITIONS}
            component={TermsAndConditionsScreen}
            options={{ headerShown: false }}
          />

          {/* Contact Us */}
          <Stack.Screen
            name={ROUTES.CONTACT_US}
            component={ContactUsScreen}
            options={{ headerShown: false }}
          />

          {/* About Us */}
          <Stack.Screen
            name={ROUTES.ABOUT_US}
            component={AboutUsScreen}
            options={{ headerShown: false }}
          />

          {/* Nitnem */}
          <Stack.Screen
            name={ROUTES.NITNEM}
            component={NitnemScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name={ROUTES.NITNEM_DETAILS}
            component={NitnemDetails}
            options={{ headerShown: false }}
          />

          {/* Carousel Images */}
          <Stack.Screen
            name={ROUTES.CAROUSEL_IMAGE_VIEW}
            component={CarouselImageView}
            options={{ headerShown: false }}
          />
        </Stack.Group>
      )}
    </Stack.Navigator>
  );
};

export default Routes;
