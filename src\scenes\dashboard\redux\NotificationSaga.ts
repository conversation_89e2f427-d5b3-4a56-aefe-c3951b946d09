import { all, call, put } from "redux-saga/effects";
import { API_URL } from "../../../services/webConstants";
import { apiClient } from "../../../services/httpServices";
import { allNotificationsFail, allNotificationsStarted, allNotificationsSuccess } from "./NotifictionSlice";
import AsyncStorage from "@react-native-async-storage/async-storage";


export function* notificationsSaga() {

    yield put(allNotificationsStarted());

    const { data, ok } = yield call(apiClient.get, API_URL.ALL_NOTIFICATIONS);
    if (ok && data) {
        yield put(allNotificationsSuccess(data));

    } else if (!ok && data) {
        yield put(allNotificationsFail());
    } else {
        yield put(allNotificationsFail());
    }
}