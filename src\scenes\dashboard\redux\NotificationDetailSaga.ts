import { all, call, put } from "redux-saga/effects";
import { API_URL } from "../../../services/webConstants";
import { apiClient } from "../../../services/httpServices";
import { notificationDetailFail, notificationDetailStarted, notificationDetailSuccess } from "./NotificationDetailSlice";


export function* notificationDetailSaga(actions: any) {
    const { payload } = actions;
    yield put(notificationDetailStarted());

    const { data, ok } = yield call(apiClient.get, `${API_URL.NOTIFICATION_DETAILS}/${payload.id}`);
    if (ok && data) {
        yield put(
            notificationDetailSuccess(data));
    } else if (!ok && data) {
        yield put(notificationDetailFail());
    } else {
        yield put(notificationDetailFail());
    }
}