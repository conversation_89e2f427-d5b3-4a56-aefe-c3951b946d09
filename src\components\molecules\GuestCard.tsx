import React from 'react';
import { Dimensions, Image, StyleSheet, Text, View } from 'react-native';
import { BOOK } from '../../utils/image-constants';
import COLORS from '../../themes/colors';

interface GuestCardProps {
    title: string;
    image: any;
}
const { width } = Dimensions.get('window');
const GuestCard: React.FC<GuestCardProps> = ({ title, image }) => {
    return (
        <View style={styles.container}>
            <View style={styles.imgContainer}>
                <Image source={image} style={styles.logo} />
            </View>
            <View style={styles.titleContainer}>
                <Text style={styles.title} numberOfLines={2}>
                    {title}
                </Text>
            </View>
        </View>
    );
};

export default GuestCard;

const styles = StyleSheet.create({
    container: {
        width: (width / 2) - 20, // Adjust the width to fit two cards with spacing
        height: 160,
        backgroundColor: COLORS.white,
        borderRadius: 12,
        alignItems: 'center',
        margin: 7,
        padding: 10,
        shadowColor: COLORS.black,
        elevation: 5,
        justifyContent: 'center',
    },
    imgContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 10,
    },
    logo: {
        width: 60,
        height: 60,
        resizeMode: 'contain',
    },
    titleContainer: {
        // flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    title: {
        fontSize: 16,
        color: COLORS.black,
        textAlign: 'center',
        fontWeight: '500'
    },
});
