import {call, put} from 'redux-saga/effects';
import {
  fetchBannersRequest,
  fetchBannersSuccess,
  fetchBannersFailure,
} from './bannerSlice';
import {apiClient} from '../../../../services/httpServices';
import {API_URL} from '../../../../services/webConstants';

export function* fetchBannersSaga() {
  console.log('inside Banner data from saga ===>');
  yield put(fetchBannersRequest());

  try {
    const {data, ok} = yield call(apiClient.get, API_URL.BANNER_DATA);
    console.log('Banner data from saga ===>', data);

    if (ok && data) {
      yield put(fetchBannersSuccess(data.data));
    } else {
      yield put(fetchBannersFailure('Failed to fetch banners'));
    }
  } catch (error) {
    yield put(
      fetchBannersFailure(
        error.message || 'An error occurred while fetching banners',
      ),
    );
  }
}
