import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    isLoading: false,
    notificationCount: 0,
};

const notificationDetailSlice = createSlice({
    name: 'notificationCount',
    initialState,
    reducers: {
        notificationCountStarted(state) {
            state.isLoading = true;
        },
        notificationCountSuccess(state, { payload }) {
            state.isLoading = false;
            state.notificationCount = payload;
        },
        notificationCountFail(state) {
            state.isLoading = false;
        },

    },
});

const { actions, reducer } = notificationDetailSlice;

export const {
    notificationCountSuccess,
    notificationCountStarted,
    notificationCountFail
} = actions;
export default reducer;