import React, {useEffect, useState} from 'react';
import {
  Image,
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Switch,
} from 'react-native';
import {Controller, useForm} from 'react-hook-form';
import CustomTextInput from '../../components/atoms/inputs/CustomTextInput';
import {useRoute} from '@react-navigation/native';
import {POST_USER_PROFILE} from './redux/AuthAction';
import {useDispatch, useSelector} from 'react-redux';
import {CustomDropdown} from '../../components/atoms/inputs/CustomDropdown';
import {AVTAR, BUSINESS_IMAGE, CAMERA_ICON} from '../../utils/image-constants';
import {hp, wp} from '../../utils/helper';
import Button from '../../components/atoms/buttons/Button';
import COLORS from '../../themes/colors';
import {launchImageLibrary} from 'react-native-image-picker';
import axios from 'axios';
import {API_URL} from '../../services/webConstants';

const BusinessDetails = () => {
  const route = useRoute();
  const {profileData, profileImage} = route.params || {};
  const {isLoading} = useSelector(state => state.auth);
  const dispatch = useDispatch();
  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    clearErrors,
    formState: {errors},
  } = useForm();
  const [selectedImage, setSelectedImage] = useState(null);
  const [categories, setCategories] = useState([]);
  // phone number
  const [isSameNumber, setIsSameNumber] = useState(false);

  // check if has business
  const [hasBusiness, setHasBusiness] = useState(false);
  const handleToggleSwitch = (value: boolean) => {
    setHasBusiness(value);
  };

  // Location states
  const [countries, setCountries] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [states, setStates] = useState([]);
  const [selectedState, setSelectedState] = useState(null);
  const [cities, setCities] = useState([]);
  const [selectedCity, setSelectedCity] = useState(null);

  const handleSubmitProfile = async (data: any, hasBusiness: boolean) => {
    const formData = new FormData();

    // Append the profile form data from personal details (profileData passed via navigation)
    Object.keys(profileData).forEach(key => {
      formData.append(key, profileData[key]);
    });

    // Only append business details if `hasBusiness` is true
    if (hasBusiness) {
      formData.append('business_name', data.businessName || '');
      formData.append('business_info', data.businessInformation || '');
      formData.append('business_category_id', data.businessCategory || '');
      formData.append('business_address', data.businessAddress || '');
      formData.append('business_contact', data.businessNumber || '');
      formData.append('business_whatsapp', data.businessWpNumber || '');
      formData.append(
        'business_established_year',
        data.businessestablishmentYear || '',
      );
      formData.append('business_email', data.businessEmail || '');

      formData.append(
        'business_city',
        selectedCity?.label || selectedCity?.value || selectedCity,
      );
      formData.append(
        'business_state',
        selectedState?.label || selectedState?.value || selectedState,
      );
      formData.append(
        'business_country',
        selectedCountry?.label || selectedCountry?.value || selectedCountry,
      );

      // Append the image to FormData
      if (selectedImage) {
        formData.append('business_image', {
          uri: selectedImage,
          name: 'business_image.jpg',
          type: 'image/jpeg',
        });
      }
    }

    // append profile image from personal details form
    if (profileImage) {
      formData.append('user_image', {
        uri: profileImage,
        type: 'image/jpeg',
        name: 'profile_image.jpg',
      });
    }

    console.log('FormData payload:', formData);
    dispatch({type: POST_USER_PROFILE, payload: formData});
  };

  // select business image
  const openGallery = () => {
    launchImageLibrary({mediaType: 'photo', quality: 0.8}, response => {
      if (response.didCancel) {
        console.log('User cancelled image picker');
      } else if (response.errorCode) {
        console.error('ImagePicker Error: ', response.errorMessage);
      } else {
        const imageUri = response.assets[0]?.uri;
        console.log('selected business image', imageUri);
        setSelectedImage(imageUri);
      }
    });
  };

  // Fetch business categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await axios.get(
          `https://punjabisamaj.phpteam.in/public/api/${API_URL.BUSINESS_CATEGORIES}`,
        );
        if (response.data.success) {
          setCategories(
            response.data.data.map(item => ({
              label: item.category,
              value: item.id.toString(),
            })),
          );
        } else {
          console.log('Failed to fetch categories');
        }
      } catch (err) {
        console.log('Error fetching categories');
      }
    };

    fetchCategories();
  }, []);

  // fetch Country State and Cities and fill the dropdown accordingly
  useEffect(() => {
    // Function to fetch country list using axios
    const getCountryList = async () => {
      try {
        const response = await axios.get(
          `https://punjabisamaj.phpteam.in/public/api/${API_URL.FETCH_ALL_COUNTRIES}`,
        );

        if (response.data && response.data.data) {
          const countryList = response.data.data.map(country => ({
            label: String(country.countryName),
            value: String(country.id),
            phoneCode: String(country.countryPhoneCode),
          }));
          setCountries(countryList);
        } else {
          console.error('No data found in the response');
        }
      } catch (error) {
        console.error('Error fetching country list', error);
      }
    };

    getCountryList();
  }, []);

  // STATE
  useEffect(() => {
    const getStateList = async () => {
      try {
        const response = await axios.get(
          `https://punjabisamaj.phpteam.in/public/api/${API_URL.FETCH_STATES_FOR_COUNTRY}/${selectedCountry?.value}`,
        );

        if (response.data && response.data.data) {
          const stateList = response.data.data.map(state => ({
            label: String(state.name),
            value: String(state.id),
          }));
          setStates(stateList);
        } else {
          console.error('No states data found in the response');
        }
      } catch (error) {
        console.error('Error fetching state list', error);
      }
    };

    if (selectedCountry?.value) {
      getStateList();
    }
  }, [selectedCountry]);

  // CITY
  useEffect(() => {
    const getCityList = async () => {
      try {
        const response = await axios.get(
          `https://punjabisamaj.phpteam.in/public/api/${API_URL.FETCH_CITIES_FOR_STATE}/${selectedState?.value}`,
        );

        if (response.data && response.data.data) {
          const cityList = response.data.data.map(city => ({
            label: String(city.cityName),
            value: String(city.id),
          }));
          setCities(cityList);
        } else {
          console.error('No cities data found in the response');
        }
      } catch (error) {
        console.error('Error fetching city list', error);
      }
    };

    if (selectedState?.value) {
      getCityList();
    }
  }, [selectedState]);

  // submit without business details
  const submitWithoutBusiness = () => {
    console.log('submit without business');
    handleSubmitProfile({}, false);
  };

  // If switch is on, copy business number to WhatsApp number
  const handleSwitchChange = value => {
    setIsSameNumber(value);
    if (value) {
      setValue('businessWpNumber', getValues('businessNumber'));
    } else {
      setValue('businessWpNumber', '');
    }
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        {/* switch to check if has business */}
        <View style={styles.switchContainer}>
          <Text style={styles.switchLabel}>Do you have a business?</Text>
          <Switch
            value={hasBusiness}
            onValueChange={value => handleToggleSwitch(value)}
            thumbColor={hasBusiness ? COLORS.primary : COLORS.lightGray}
            trackColor={{true: COLORS.primary, false: COLORS.gray}}
          />
        </View>
        {/* Informational Message */}
        {hasBusiness ? (
          <Text style={styles.infoText}>
            Please fill all the relevant fields in the business details section
            before submitting.
          </Text>
        ) : (
          <Text style={styles.infoText}>
            If you don't have a business, you can directly submit your profile
            with personal details only.
          </Text>
        )}

        {/* Form Fields */}
        {hasBusiness ? (
          <>
            <ScrollView
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="always">
              <View style={styles.imageContainer}>
                <TouchableOpacity onPress={openGallery}>
                  <Image
                    style={styles.image}
                    source={
                      selectedImage ? {uri: selectedImage} : BUSINESS_IMAGE
                    }
                    resizeMode="cover"
                  />
                  <View style={styles.cameraIconContainer}>
                    <Image source={CAMERA_ICON} style={styles.cameraIcon} />
                  </View>
                </TouchableOpacity>
              </View>

              <Text style={styles.heading}>Enter Your Business Details</Text>

              <View style={{padding: 5}}>
                {/* Business Name Input */}
                <Controller
                  control={control}
                  name="businessName"
                  rules={{required: 'Business Name is required'}}
                  render={({field: {onChange, value}}) => (
                    <CustomTextInput
                      placeholder="Business Name *"
                      label="Business Name"
                      value={value}
                      onChangeText={onChange}
                    />
                  )}
                />
                {errors.businessName && (
                  <Text style={styles.errorText}>
                    {errors.businessName.message}
                  </Text>
                )}

                {/* Business Category Dropdown */}
                <CustomDropdown
                  control={control}
                  name="businessCategory"
                  placeholder="Select Business Category"
                  data={categories || []}
                  onChange={item => {
                    setValue('businessCategory', item.value);
                    clearErrors('businessCategory');
                  }}
                  isSearch={true}
                  icon="briefcase"
                />

                {/* Business Information Input */}
                <Controller
                  control={control}
                  name="businessInformation"
                  rules={{required: 'Business information is required'}}
                  render={({field: {onChange, value}}) => (
                    <CustomTextInput
                      placeholder="Business Information *"
                      label="Business Information"
                      value={value}
                      onChangeText={onChange}
                    />
                  )}
                />
                {errors.businessInformation && (
                  <Text style={styles.errorText}>
                    {errors.businessInformation.message}
                  </Text>
                )}

                {/* Business Location (City, State, Country) - All are dropdowns */}

                {/* Country dropdown */}
                <CustomDropdown
                  control={control}
                  name="businessCountry"
                  placeholder="Business Country"
                  data={countries}
                  onChange={value => {
                    setSelectedCountry(value);
                    setSelectedState(null);
                    setValue('country', value);
                    setValue('state', {});
                    setValue('city', {});
                    setCities([]);
                    if (value) {
                      clearErrors('country');
                    }
                  }}
                  isSearch={true}
                  icon={'flag'}
                />
                {/* State dropdown */}
                <CustomDropdown
                  control={control}
                  name="businessState"
                  placeholder="Business State"
                  data={states}
                  onChange={value => {
                    setSelectedState(value);
                    setValue('state', value);
                    setValue('city', {});
                    if (value) {
                      clearErrors('state');
                    }
                  }}
                  disabled={!selectedCountry}
                  isSearch={true}
                  icon={'globe'}
                />
                <CustomDropdown
                  control={control}
                  name="businessCity"
                  placeholder="Business City"
                  data={cities}
                  onChange={(value: any) => {
                    setSelectedCity(value);
                    setValue('city', value);
                    if (value) {
                      clearErrors('city');
                    }
                  }}
                  disabled={!selectedState}
                  isSearch={true}
                  icon={'map-marker'}
                />

                {/* Business Address Input */}
                <Controller
                  control={control}
                  name="businessAddress"
                  rules={{required: 'Business address is required'}}
                  render={({field: {onChange, value}}) => (
                    <CustomTextInput
                      placeholder="Business Address *"
                      label="Business Address"
                      value={value}
                      onChangeText={onChange}
                    />
                  )}
                />
                {errors.businessAddress && (
                  <Text style={styles.errorText}>
                    {errors.businessAddress.message}
                  </Text>
                )}

                {/* Business Email Input */}
                <Controller
                  control={control}
                  name="businessEmail"
                  rules={{
                    required: 'Business Email is required',
                    pattern: {
                      value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                      message:
                        'Invalid email format. Example: <EMAIL>',
                    },
                  }}
                  render={({field: {onChange, value}}) => (
                    <CustomTextInput
                      placeholder="Business Email *"
                      label="Business Email"
                      value={value}
                      onChangeText={onChange}
                      keyboardType="email-address"
                    />
                  )}
                />
                {errors.businessEmail && (
                  <Text style={styles.errorText}>
                    {errors.businessEmail.message}
                  </Text>
                )}

                {/* Business Establishment Year Input */}
                <Controller
                  control={control}
                  name="businessestablishmentYear"
                  rules={{
                    required: 'Business Establishment Year is required',
                    pattern: {
                      value: /^(19|20)\d{2}$/,
                      message: 'Please enter a valid year (e.g., 2024)',
                    },
                  }}
                  render={({field: {onChange, value}}) => (
                    <CustomTextInput
                      placeholder="Business Establishment Year"
                      label="Business Establishment Year"
                      value={value}
                      onChangeText={onChange}
                      keyboardType="numeric"
                    />
                  )}
                />
                {errors.businessestablishmentYear && (
                  <Text style={styles.errorText}>
                    {errors.businessestablishmentYear.message}
                  </Text>
                )}

                {/* Business Contact Number */}
                <Controller
                  control={control}
                  name="businessNumber"
                  rules={{
                    required: 'Business Number is required',
                    pattern: {
                      value: /^[0-9]{10}$/,
                      message: 'Invalid Number. It must be a 10-digit number.',
                    },
                  }}
                  render={({field: {onChange, value}}) => (
                    <CustomTextInput
                      placeholder="Business Contact Number *"
                      label="Business Contact Number"
                      value={value}
                      onChangeText={onChange}
                      keyboardType="phone-pad"
                    />
                  )}
                />
                {errors.businessNumber && (
                  <Text style={styles.errorText}>
                    {errors.businessNumber.message}
                  </Text>
                )}

                {/* Switch to populate WhatsApp number with business number */}
                <View style={styles.smallSwitchContainer}>
                  <Text>Use the same number for WhatsApp?</Text>
                  <Switch
                    value={isSameNumber}
                    onValueChange={handleSwitchChange}
                  />
                </View>

                {/* Business WhatsApp Number */}
                <Controller
                  control={control}
                  name="businessWpNumber"
                  rules={{
                    pattern: {
                      value: /^[0-9]{10}$/, // Ensures it's exactly 10 digits
                      message: 'Invalid Number. It must be a 10-digit number.',
                    },
                  }}
                  render={({field: {onChange, value}}) => (
                    <CustomTextInput
                      placeholder="Business WhatsApp Number"
                      label="Business WhatsApp Number"
                      value={value}
                      onChangeText={onChange}
                      keyboardType="phone-pad"
                    />
                  )}
                />
                {errors.businessWpNumber && (
                  <Text style={styles.errorText}>
                    {errors.businessWpNumber.message}
                  </Text>
                )}
              </View>
            </ScrollView>

            <View style={styles.btnContainer}>
              {/* Submit Button */}
              <Button
                title="Create your Profile"
                onPress={handleSubmit(handleSubmitProfile)}
                loading={isLoading}
              />
            </View>
          </>
        ) : (
          <View style={styles.btnContainer}>
            <Button
              title="Submit Profile"
              onPress={submitWithoutBusiness}
              loading={isLoading}
            />
          </View>
        )}
      </View>
    </View>
  );
};

export default BusinessDetails;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.secondary,
    paddingHorizontal: wp(5),
  },

  // switch container
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: hp(2),
    paddingHorizontal: wp(3),
    backgroundColor: COLORS.lightGray,
    borderRadius: wp(2),
    marginVertical: hp(2),
    elevation: 2,
    shadowColor: COLORS.black,
    shadowOpacity: 0.2,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 4,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.black,
  },

  smallSwitchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: hp(1),
  },

  infoText: {
    fontSize: 14,
    color: COLORS.primary,
    marginVertical: hp(1),
    textAlign: 'center',
    fontWeight: '500',
  },

  // imageContainer: {
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   paddingVertical: hp(1),
  //   position: 'relative',
  // },
  // bannerImage: {
  //   width: wp(90),
  //   height: wp(45),
  //   borderRadius: 10,
  //   borderWidth: 2,
  //   borderColor: COLORS.imgBorder,
  //   backgroundColor: COLORS.lightGray,
  // },

  imageContainer: {
    position: 'relative',
    width: '100%',
    aspectRatio: 16 / 9,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    resizeMode: 'cover',
    borderWidth: 2,
    borderColor: COLORS.imgBorder,
    backgroundColor: COLORS.lightGray,
  },

  cameraIconContainer: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: COLORS.primary,
    width: wp(10),
    height: wp(10),
    borderRadius: wp(10) / 2,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
  },
  cameraIcon: {
    width: wp(5),
    height: wp(5),
    tintColor: 'white',
    resizeMode: 'contain',
  },

  heading: {
    textAlign: 'center',
    marginTop: hp(1),
    color: COLORS.black,
    fontSize: 20,
    fontWeight: '600',
  },
  btnContainer: {
    width: '100%',
    marginBottom: hp(3),
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 5,
  },
});
