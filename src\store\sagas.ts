import {all, takeLatest} from 'redux-saga/effects';
import {
  LOG_ERROR,
  LOGIN,
  POST_USER_PROFILE,
} from '../scenes/auth/redux/AuthAction';
import {authSaga, errorLogSaga, loginSaga} from '../scenes/auth/redux/AuthSaga';
import {notificationsSaga} from '../scenes/dashboard/redux/NotificationSaga';
import {ALL_NOTIFICATIONS} from '../scenes/dashboard/redux/NotficationAction';
import {NOTIFICATION_DETAILS} from '../scenes/dashboard/redux/NotificationDetailAction';
import {notificationDetailSaga} from '../scenes/dashboard/redux/NotificationDetailSaga';
import {READ_NOTIFICATION} from '../scenes/dashboard/redux/ReadNotificationAction';
import readNotificationSaga from '../scenes/dashboard/redux/ReadNotificationSaga';
import {NOTIFICATIONS_COUNT} from '../scenes/dashboard/redux/NotificationCountAction';
import {notificationsCountSaga} from '../scenes/dashboard/redux/NotificationCountSaga';
import {INCREASE_COUNT} from '../scenes/dashboard/redux/IncreaseCountAction';
import increaseCountSaga from '../scenes/dashboard/redux/IncreaseCountSaga';
import {
  FETCH_ALL_BUSINESSES,
  FETCH_BUSINESS_BY_ID,
} from '../scenes/dashboard/redux/business/businessAction';
import {
  fetchBusinessDetailsSaga,
  fetchBusinessesSaga,
} from '../scenes/dashboard/redux/business/businessSaga';
import {FETCH_SETTINGS_DATA} from '../scenes/dashboard/redux/settings/settingsAction';
import {fetchSettingsSaga} from '../scenes/dashboard/redux/settings/settingsSaga';
import {FETCH_BANNER_DATA} from '../scenes/dashboard/redux/banner/bannerAction';
import {fetchBannersSaga} from '../scenes/dashboard/redux/banner/bannerSaga';
import {fetchNitnemSaga} from '../scenes/dashboard/redux/nitnem/nitnemSaga';
import {FETCH_NITNEM} from '../scenes/dashboard/redux/nitnem/nitnemAction';
export default function* sagas() {
  yield all([
    takeLatest(POST_USER_PROFILE, authSaga),
    takeLatest(LOGIN, loginSaga),
    takeLatest(ALL_NOTIFICATIONS, notificationsSaga),
    takeLatest(NOTIFICATION_DETAILS, notificationDetailSaga),
    takeLatest(READ_NOTIFICATION, readNotificationSaga),
    takeLatest(NOTIFICATIONS_COUNT, notificationsCountSaga),
    takeLatest(INCREASE_COUNT, increaseCountSaga),
    takeLatest(FETCH_ALL_BUSINESSES, fetchBusinessesSaga),
    takeLatest(FETCH_BUSINESS_BY_ID, fetchBusinessDetailsSaga),
    takeLatest(FETCH_SETTINGS_DATA, fetchSettingsSaga),
    takeLatest(FETCH_BANNER_DATA, fetchBannersSaga),
    // error log
    takeLatest(LOG_ERROR, errorLogSaga),
    takeLatest(FETCH_NITNEM, fetchNitnemSaga),
  ]);
}
