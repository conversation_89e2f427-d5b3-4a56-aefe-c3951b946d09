import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import Routes from './Routes';
import { navigationRef } from './RootNavigation';
import { Linking } from 'react-native';
import messaging from '@react-native-firebase/messaging';
import auth from '@react-native-firebase/auth';
import { useDispatch } from 'react-redux';
import { setNotificationId } from '../scenes/auth/redux/AuthSlice';
import { INCREASE_COUNT } from '../scenes/dashboard/redux/IncreaseCountAction';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAreaView } from 'react-native-safe-area-context';
import COLORS from '../themes/colors';

function buildDeepLinkFromNotificationData(data): string | null {
  const notificationId = data?.notificationId || '7';
  return `myapp://${data?.navigationId}/${notificationId}`;
}

const AppNavigation = () => {
  const dispatch = useDispatch();
  useEffect(() => {
    const unsubscribe = messaging().onMessage(async (remoteMessage) => {

      const storedCount = await AsyncStorage.getItem("user_alert_count");
      const initialCount = storedCount ? JSON.parse(storedCount) + 1 : 0;
      console.log("............", initialCount)
      dispatch({
        type: INCREASE_COUNT, payload: {
          alert_count: initialCount
        }

      });
    });

    return () => unsubscribe();
  }, [dispatch]);


  const linking = {
    prefixes: ['myapp://'],
    config: {
      screens: {
        LOGIN: 'login',
        NOTIFICATION_DETAILS: {
          path: 'NOTIFICATION_DETAILS/:notificationId',
          parse: {
            notificationId: id => id,
          },
        },
      },
    },

    async getInitialURL() {
      // Check for deep link when app is cold-started.
      const url = await Linking.getInitialURL();
      if (url) {
        return url;
      }

      // Check for notification data when app is cold-started.
      const message = await messaging().getInitialNotification();
      if (message?.data) {
        const isAuthenticated = auth().currentUser != null;
        const deeplinkURL = buildDeepLinkFromNotificationData(message.data);

        if (isAuthenticated) {
          return deeplinkURL;
        } else {
          // Dispatch to Redux for unauthenticated users
          if (message.data.notificationId) {
            console.log(
              'dispatching notification id to Redux',
              message.data.notificationId,
            );
            dispatch(setNotificationId(message.data.notificationId));
          }
          return 'myapp://LOGIN';
        }
      }

      return null;
    },

    subscribe(listener: (url: string) => void) {
      const onReceiveURL = ({ url }: { url: string }) => listener(url);

      // Listen to deep links from other sources (e.g., browser)
      const linkingSubscription = Linking.addEventListener('url', onReceiveURL);

      // Listen to notification taps when the app is in the background
      const unsubscribe = messaging().onNotificationOpenedApp(remoteMessage => {
        if (remoteMessage?.data) {
          const isAuthenticated = auth().currentUser != null;
          const url = buildDeepLinkFromNotificationData(remoteMessage.data);

          if (isAuthenticated) {
            // Authenticated user, navigate to NotificationDetails
            if (url) {
              listener(url);
            }
          } else {
            // Unauthenticated user, navigate to Login page and dispatch notificationId to Redux
            listener('myapp://LOGIN');

            // Dispatch the notificationId to Redux for unauthenticated users
            if (remoteMessage.data.notificationId) {
              console.log(
                'Dispatching notificationId to Redux for unauthenticated user:',
                remoteMessage.data.notificationId,
              );
              dispatch(setNotificationId(remoteMessage.data.notificationId));
            }
          }
        }
      });

      return () => {
        linkingSubscription.remove();
        unsubscribe();
      };
    },
  };
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: COLORS.primary }}>
    <NavigationContainer ref={navigationRef} linking={linking}>
      <Routes />
    </NavigationContainer>
    </SafeAreaView>
  );
};

export default AppNavigation;
