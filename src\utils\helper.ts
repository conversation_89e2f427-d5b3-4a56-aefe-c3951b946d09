import {
  heightPercentageToDP,
  widthPercentageToDP,
} from 'react-native-responsive-screen';
import moment from 'moment';

const wp = (value: string | number): number => widthPercentageToDP(value);
const hp = (value: string | number): number => heightPercentageToDP(value);

/**
 * Format a date string with both date and time.
 *
 * @param {string} date - The date in 'YYYY-MM-DD' format.
 * @param {string} time - The time in 'hh:mm:ss a' format.
 * @returns {string} - The formatted date and time.
 */
const formatNotificationDate = (date, time) => {
  return moment(`${date} ${time}`, 'YYYY-MM-DD hh:mm:ss a').format(
    'D MMMM, YYYY h:mm A',
  );
};

export {wp, hp, formatNotificationDate};
