import React, {useEffect, useRef} from 'react';
import {Controller} from 'react-hook-form';
import {
  Animated,
  Easing,
  Image,
  Pressable,
  StyleSheet,
  Text,
  TextInput,
  View,
} from 'react-native';

export const FloatingLabelInput = ({
  icon: Icon,
  placeholder,
  control,
  rules,
  name,
  rightIcon = undefined,
  onRightIconPress,
  keyboardType = 'default',
  maxLength,
  prefix,
  ...props
}) => {
  const transY = useRef(new Animated.Value(0)).current;

  const watchValue = control._formValues[name];

  useEffect(() => {
    if (watchValue || prefix) {
      animateTransform(-25);
    }
  }, [watchValue, prefix]);

  const animateTransform = number => {
    Animated.timing(transY, {
      toValue: number,
      duration: 300,
      useNativeDriver: true,
      easing: Easing.ease,
    }).start();
  };

  const handleFocus = () => {
    animateTransform(-25);
  };

  const handleBlur = value => {
    if (!value && !prefix) {
      animateTransform(0);
    }
  };

  const transX = transY.interpolate({
    inputRange: [-40, 0],
    outputRange: [-5, 0],
    extrapolate: 'clamp',
  });

  const labelBackgroundColor = transY.interpolate({
    inputRange: [-25, 0],
    outputRange: ['#191405', 'transparent'],
    extrapolate: 'clamp',
  });

  return (
    <View style={styles.inputWrapper}>
      <Controller
        control={control}
        rules={rules}
        name={name}
        render={({field: {onChange, onBlur, value}, fieldState: {error}}) => (
          <>
            <View style={[styles.inputContainer, error && styles.inputError]}>
              {Icon && (
                <View style={styles.iconContainer}>
                  <Image style={styles.icon} source={Icon} />
                </View>
              )}
              {placeholder && (
                <Animated.View
                  style={[
                    styles.labelContainer,
                    {
                      transform: [{translateY: transY}, {translateX: transX}],
                      backgroundColor: labelBackgroundColor,
                    },
                  ]}>
                  <Text style={styles.labelText}>{placeholder}</Text>
                </Animated.View>
              )}
              {prefix && <Text style={styles.prefixText}>{prefix}</Text>}
              <TextInput
                style={styles.input}
                onChangeText={text => {
                  let transformedText = text;
                  if (name === 'pan') {
                    transformedText = text.toUpperCase();
                  }
                  if (name === 'vitals') {
                    transformedText = text
                      .replace(/-/g, '')
                      .replace(/(\d{2})(?=\d)/g, '$1-');
                  }
                  onChange(transformedText);
                  // const transformedText =
                  //   name === 'pan' ? text.toUpperCase() : text;
                  // onChange(transformedText);
                }}
                onBlur={() => {
                  onBlur();
                  handleBlur(value);
                }}
                value={value}
                onFocus={handleFocus}
                keyboardType={keyboardType}
                maxLength={maxLength}
                {...props}
              />
              {rightIcon && (
                <Pressable onPress={onRightIconPress}>
                  <Image style={styles.rightIcon} source={rightIcon} />
                </Pressable>
              )}
            </View>
            {error && (
              <Text style={styles.errorText}>
                {error.message || 'Required'}
              </Text>
            )}
          </>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  inputWrapper: {},
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: '#D9D9D9',
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 10,
    height: 50,
  },
  iconContainer: {
    marginRight: 5,
  },
  icon: {
    width: 22,
    height: 22,
    resizeMode: 'contain',
  },
  rightIcon: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
  },
  prefixText: {
    color: '#fff',
  },
  input: {
    flex: 1,
    color: '#fff',
  },
  inputError: {
    borderColor: 'red',
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 2,
    marginBottom: 0,
  },
  labelContainer: {
    position: 'absolute',
    left: 30,
    padding: 2,
    paddingHorizontal: 8,
    borderRadius: 10,
  },
  labelText: {
    color: '#fff',
    fontSize: 12,
  },
});
