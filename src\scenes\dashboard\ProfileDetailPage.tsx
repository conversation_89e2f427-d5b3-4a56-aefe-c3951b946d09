import { Image, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import CustomHeader from '../../components/molecules/CustomHeader'
import { ScrollView } from 'react-native-gesture-handler'
import COLORS from '../../themes/colors'
import { MAIN_PROFILE } from '../../utils/image-constants'
import { hp, wp } from '../../utils/helper'

const ProfileDetailPage = () => {
    return (
        <View style={styles.mainContainer}>
            <CustomHeader title="Sushant" showBackButton={true} />
            <ScrollView>
                <View style={styles.container}>
                    <View style={styles.imageContainer}>
                        <Image source={MAIN_PROFILE} style={styles.profileImg} />
                        <View style={styles.nameBackdrop}>
                            <Text style={styles.nameText}> Sushant</Text>
                        </View>
                    </View>
                    <View style={styles.personalInfoSection}>
                        <Text style={styles.personalInfo}>Personal Information</Text>
                        <View style={styles.field}>
                            <Text style={styles.upperText}>Name</Text>
                            <Text style={styles.lowerText}>Sushan<PERSON> Singh</Text>
                        </View>
                        <View style={styles.field}>
                            <Text style={styles.upperText}>Father's Name</Text>
                            <Text style={styles.lowerText}>Karsn Singh</Text>
                        </View>
                        <View style={styles.field}>
                            <Text style={styles.upperText}>Age</Text>
                            <Text style={styles.lowerText}> 28 Yrs</Text>
                        </View>
                        <View style={styles.field}>
                            <Text style={styles.upperText}>Date Of Birth</Text>
                            <Text style={styles.lowerText}>11/03/1995</Text>
                        </View>
                        <View style={styles.field}>
                            <Text style={styles.upperText}>Mobile Numbet</Text>
                            <Text style={styles.lowerText}>9876543210</Text>
                        </View>
                        <View style={styles.field}>
                            <Text style={styles.upperText}>Whatsapp Number</Text>
                            <Text style={styles.lowerText}>9876543210</Text>
                        </View>
                        <View style={styles.field}>
                            <Text style={styles.upperText}>State</Text>
                            <Text style={styles.lowerText}>Chhattisgarh</Text>
                        </View>
                        <View style={styles.field}>
                            <Text style={styles.upperText}>City</Text>
                            <Text style={styles.lowerText}>Bilaspur</Text>
                        </View>

                    </View>
                </View>
            </ScrollView >
        </View >
    )
}

export default ProfileDetailPage

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: COLORS.secondary,
    },
    container: {
        flex: 1,

    },
    imageContainer: {
        marginTop: hp(1),
        backgroundColor: COLORS.black,
        resizeMode: 'cover',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',

    },
    profileImg: {
        width: 280,
        resizeMode: 'cover',
        height: hp(40),
    },
    nameBackdrop: {
        position: 'absolute',
        backgroundColor: 'rgba(0, 0, 0, 0.79)',
        height: 67,
        width: '100%',
        bottom: 0,
        justifyContent: 'center'
    },
    nameText: {
        fontSize: 20,
        fontWeight: '600',
        color: COLORS.white,
        paddingStart: wp(5),
    },
    personalInfoSection: {
        paddingHorizontal: wp(8),
        marginTop: hp(2),
    },
    personalInfo: {
        fontSize: 20,
        fontWeight: '600',
        color: COLORS.black,
    },
    field: {
        marginTop: hp(2),
        marginBottom: hp(1)
    },
    upperText: {
        fontSize: 14,
        fontWeight: '500',
        color: COLORS.black
    },
    lowerText: {
        fontSize: 14
    }
})