import { Image, StyleSheet, Text, View, Dimensions } from 'react-native';
import React from 'react';
import { BOOK } from '../../utils/image-constants';
import COLORS from '../../themes/colors';

interface NitnemCardProps {
  title: string;
  images: any;
}

const NitnemCard: React.FC<NitnemCardProps> = ({ title, image }) => {
  const screenWidth = Dimensions.get('window').width;

  return (
    <View style={[styles.containerInner, { width: screenWidth * 0.3 }]}>
      <View style={styles.imgContainer}>
        <Image source={image} style={styles.logo} />
      </View>

      <View style={styles.titleContainer}>
        <Text style={styles.title} numberOfLines={2}>
          {title}
        </Text>
      </View>

    </View>
  );
};

export default NitnemCard;

const styles = StyleSheet.create({
  containerInner: {
    height: 140,
    padding: 15,
    backgroundColor: COLORS.white,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: COLORS.black,
    elevation: 5,
    marginHorizontal: 5,
    marginVertical: 5,
    justifyContent: 'space-between', // This ensures space between image and title
  },
  imgContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10, // Space between image and text
  },
  logo: {
    width: 60,
    height: 60,
    resizeMode: 'contain',
  },
  title: {
    fontSize: 16,
    color: COLORS.black,
    textAlign: 'center',
    flexShrink: 1,

  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center'
  }
});
