import { NavigationContainerRef } from '@react-navigation/native';
import * as React from 'react';
export const navigationRef = React.createRef<NavigationContainerRef<any>>();

export function navigate(name: string, params: object) {
    navigationRef.current?.navigate(name, params);
}

export function goBack() {
    navigationRef.current?.goBack();
}

export function push(name: string, params: object) {
    navigationRef.current?.push(name, params);
}

export function navReset(name: string) {
    navigationRef.current?.resetRoot({ index: 1, routes: [{ name }] });
}
