import { call, put } from 'redux-saga/effects';
import { API_URL } from '../../../services/webConstants';
import { apiClient } from '../../../services/httpServices';
import { ALL_NOTIFICATIONS } from './NotficationAction';
import { readNotificationFailed, readNotificationStarted, readNotificationSuccess } from './ReadNotificationSlice';


function* readNotificationSaga(action) {
    const { payload } = action
    try {
        yield put(readNotificationStarted());

        const response = yield call(
            apiClient.post,
            API_URL.READ_NOTIFICATION,
            payload
        );
        yield put(readNotificationSuccess(response.data));
        yield put({ type: ALL_NOTIFICATIONS })

    } catch (error) {
        yield put(readNotificationFailed());
    }
}

export default readNotificationSaga;