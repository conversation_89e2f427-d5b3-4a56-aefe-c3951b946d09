import {createSlice} from '@reduxjs/toolkit';

const bannerSlice = createSlice({
  name: 'banner',
  initialState: {
    banners: [],
    loading: false,
    error: null,
  },
  reducers: {
    fetchBannersRequest(state) {
      state.loading = true;
      state.error = null;
    },
    fetchBannersSuccess(state, action) {
      state.loading = false;
      state.banners = action.payload;
    },
    fetchBannersFailure(state, action) {
      state.loading = false;
      state.error = action.payload;
    },
  },
});

export const {fetchBannersRequest, fetchBannersSuccess, fetchBannersFailure} =
  bannerSlice.actions;

export default bannerSlice.reducer;
