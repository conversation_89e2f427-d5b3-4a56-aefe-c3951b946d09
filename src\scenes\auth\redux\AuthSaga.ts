import { call, put } from 'redux-saga/effects';
import { API_URL } from '../../../services/webConstants';
import { apiClient } from '../../../services/httpServices';
import {
  userLoginFail,
  userLoginStarted,
  userLoginSuccess,
  userProfilePostFail,
  userProfilePostStarted,
  userProfilePostSuccess,
} from './AuthSlice';
import { navigate } from '../../../navigation/RootNavigation';
import { Alert } from 'react-native';
import { showMessage } from 'react-native-flash-message';
import { LOG_ERROR } from './AuthAction';

export function* authSaga({ payload }: any) {
  console.log('payload from signup saga ==>', payload);

  yield put(userProfilePostStarted());
  // Set headers for multipart/form-data
  const config = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  };

  const { data, ok } = yield call(
    apiClient.post,
    API_URL.USER_SIGNUP,
    payload,
    config,
  );
  console.log('response from signup saga ==>', data);
  if (ok && data && data?.success) {
    if (!data?.is_verified) {
      Alert.alert(
        'Account Pending Verification',
        'Your account has been created successfully. You will be notified once your account is approved. You can log in after approval.',
        [{ text: 'OK' }],
      );
      yield put(userProfilePostSuccess(data));
      navigate('LOGIN');
    } else {
      yield put(userProfilePostSuccess(data));
      console.log('response from signup saga success ==>', data);
      yield put(userLoginSuccess(data));
      showMessage({
        message: data?.message ? data?.message : 'Login Successfully',
        type: 'success',
      });
    }
  } else if (!ok && data) {
    yield put(userProfilePostFail());
    console.log('response from signup saga else fail ==>');
    showMessage({
      message: data?.message ? data?.message : 'Something went wrong!',
      type: 'danger',
    });
  } else {
    console.log('response from signup saga fail ==>');
    yield put(userProfilePostFail());
    showMessage({
      message: data?.message ? data?.message : 'Something went wrong!',
      type: 'danger',
    });
  }
}

// login

// export function* loginSaga({payload}: any) {
//   yield put(userLoginStarted());

//   const {data, ok, status} = yield call(apiClient.post, API_URL.LOGIN, payload);
//   console.log('login success', data);

//   if (ok && status === 200 && data) {
//     // yield put(userLoginSuccess(data));

//     if (data?.is_verified) {
//       // Show an alert and navigate to login
//       Alert.alert(
//         'Account Pending Verification',
//         'Your account has not been verified. You will be notified once it is approved!',
//         [{text: 'OK'}],
//       );
//       navigate('LOGIN');
//     } else {
//       // Proceed with login success
//       console.log('inside login else');
//       yield put(userLoginSuccess(data));
//     }
//   } else if (!ok && status === 404 && data) {
//     yield put(userLoginFail());
//     navigate('PROFILE_SETUP', {
//       phoneNumber: payload.mobile,
//       firebase_token: payload.firebase_token,
//     });
//   } else {
//     yield put(userLoginFail());
//   }
// }

// test login saga
// export function* loginSaga({payload}: any) {
//   yield put(userLoginStarted());

//   const {data, ok, status} = yield call(apiClient.post, API_URL.LOGIN, payload);
//   console.log('login response:', data);

//   if (ok && status === 200 && data) {
//     // Proceed with login success
//     yield put(userLoginSuccess(data));
//     showMessage({
//       message: data?.message ? data?.message : 'Login Successfully',
//       type: 'success',
//     });
//   } else if (status === 403 && data?.is_verified === false) {
//     // Account is not verified, show alert and navigate to login
//     Alert.alert(
//       'Account Pending Verification',
//       'Your account has not been verified. You will be notified once it is approved!',
//       [{text: 'OK'}],
//     );
//     yield put(userLoginFail());
//     navigate('LOGIN');
//   } else if (!ok && status === 404 && data) {
//     // Account not found, navigate to profile setup
//     yield put(userLoginFail());
//     navigate('PROFILE_SETUP', {
//       phoneNumber: payload.mobile,
//       firebase_token: payload.firebase_token,
//     });
//   } else {
//     // Handle any unexpected errors
//     yield put(userLoginFail());
//     showMessage({
//       message: data?.message ? data?.message : 'Something went wrong!',
//       type: 'danger',
//     });
//   }
// }

// error log saga
export function* errorLogSaga({ payload }: any) {
  console.log('Inside Error Log Saga payload', payload);
  const { data, ok } = yield call(apiClient.post, API_URL.ERROR_LOG, payload);
  console.log('Inside Error Log Saga data', data);
}

// new login saga
export function* loginSaga({ payload }: any) {
  yield put(userLoginStarted());

  try {

    console.log('payload', payload)
    const { data, ok, status } = yield call(
      apiClient.post,
      API_URL.LOGIN,
      payload,
    );
    console.log('login response:', data);

    if (ok && status === 200 && data) {
      // Proceed with login success
      yield put(userLoginSuccess(data));
      showMessage({
        message: data?.message ? data?.message : 'Login Successfully',
        type: 'success',
      });
    } else if (status === 403 && data?.is_verified === false) {
      // Account is not verified, show alert and navigate to login
      Alert.alert(
        'Account Pending Verification',
        'Your account has not been verified. You will be notified once it is approved!',
        [{ text: 'OK' }],
      );
      yield put(userLoginFail());
      navigate('LOGIN');
    } else if (!ok && status === 404 && data) {
      // Account not found, navigate to profile setup
      yield put(userLoginFail());
      navigate('PROFILE_SETUP', {
        phoneNumber: payload.mobile,
        firebase_token: payload.firebase_token,
      });
    } else {
      // Handle any unexpected errors
      yield put(userLoginFail());
      showMessage({
        message: data?.message ? data?.message : 'Something went wrong!',
        type: 'danger',
      });

      // Dispatch an error log action
      yield put({
        type: LOG_ERROR,
        payload: {
          text: `Login Failed || Error details: ${data?.message || 'Unknown error'
            } | Payload: ${JSON.stringify(payload)}`,
        },
      });
    }
  } catch (error) {
    console.error('Unexpected error during login:', error);

    // Handle unexpected errors
    yield put(userLoginFail());
    showMessage({
      message: 'An unexpected error occurred. Please try again.',
      type: 'danger',
    });

    // Dispatch an error log action
    yield put({
      type: LOG_ERROR,
      payload: {
        text: `Unexpected Error during Login || ${error.message || 'Unknown error'
          }`,
      },
    });
  }
}
