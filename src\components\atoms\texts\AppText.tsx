import React from 'react';
import { Dimensions, StyleSheet, Text, TextStyle } from 'react-native';
import fontFamily from '../../../utils/fonts';
import COLORS from '../../../themes/colors';

type TextExtendedProps = {
  bold?: boolean;
  semiBold?: boolean;
  size?: number;
  color?: string;
  style?: TextStyle;
  numberOfLines?: number;
  children: React.ReactNode;
  ellipsizeMode?: 'head' | 'middle' | 'tail' | 'clip';
  onPress?: (event: any) => void;
};

const TextExtended: React.FC<TextExtendedProps> = ({
  bold,
  semiBold,
  size,
  color,
  style,
  numberOfLines,
  children,
  ellipsizeMode,
}) => {
  const { width: SCREEN_WIDTH } = Dimensions.get('window');

  const getFontFamily = () => {
    if (bold) {
      return fontFamily.Bold;
    } else if (semiBold) {
      return fontFamily.SemiBold;
    } else {
      return fontFamily.Regular;
    }
  };

  const fontScale = (size: string | number) => {
    if (typeof size === 'number') {
      return (size * SCREEN_WIDTH) / 400;
    } else {
      return (parseInt(size) * SCREEN_WIDTH) / 400;
    }
  };

  const getFontSize = () => {
    return size !== undefined ? fontScale(size) : fontScale(14);
  };

  return (
    <Text
      style={StyleSheet.flatten([
        {
          color: color || COLORS.black,
          fontFamily: getFontFamily(),
          fontSize: getFontSize(),
        },
        styles.text,
        style,
      ])}
      numberOfLines={numberOfLines || 0}
      ellipsizeMode={ellipsizeMode || 'tail'}>
      {children}
    </Text>
  );
};
TextExtended.defaultProps = {
  bold: false,
  semiBold: false,
  size: 15,
  color: COLORS.black,
  style: undefined,
  numberOfLines: 4,
  onPress: () => undefined,
  ellipsizeMode: undefined,
};

export default TextExtended;

const styles = StyleSheet.create({
  text: {},
});
