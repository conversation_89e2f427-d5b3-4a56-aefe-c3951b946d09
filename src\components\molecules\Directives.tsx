import React from 'react';
import { View, Text, Pressable, Image, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import COLORS from '../../themes/colors';
import { PUNJABI_ICON } from '../../utils/image-constants';

const DirectivesSection = ({
  handleNavigatePunjabiSamaj,
  handleNavigateBusinessList,
}) => {
  return (
    <View style={styles.directives}>
      <Text style={styles.directivesText}>Our Community</Text>
      <View style={styles.btnOuter}>
        {/* Punjabi Samaj Matrimony : hide for now*/}
        {/* <Pressable style={styles.listItem} onPress={handleNavigatePunjabiSamaj}>
          <View style={styles.listItemInner}>
            <View style={styles.logo}>
              <Image source={PUNJABI_ICON} style={styles.logoImg} />
            </View>
            <Text style={styles.listText}>Punjabi Samaj </Text>
          </View>
          <Icon
            name="angle-right"
            size={24}
            color={COLORS.primary}
          />
        </Pressable>*/}


        {/* Business Directory */}
        <Pressable style={styles.listItem} onPress={handleNavigateBusinessList}>
          <View style={styles.listItemInner}>
            <View style={styles.logo}>
              <Icon
                name="briefcase"
                size={24}
                color={COLORS.primary}
                style={styles.icon}
              />
            </View>
            <Text style={styles.listText}>Business Directory</Text>
          </View>
          <View style={styles.arrowContainer}>
            <Icon name="angle-right" size={22} color={COLORS.primary} />
          </View>
        </Pressable>
        {/* <View style={styles.divider} /> */}

      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  directives: {
    marginTop: 15,
    paddingBottom: 10,
    borderRadius: 12,
    paddingHorizontal: 5,
  },
  directivesText: {
    fontSize: 22,
    fontWeight: '600',
    color: COLORS.black,
    marginBottom: 10,
  },
  btnOuter: {
    width: '100%',
    borderRadius: 12,
    backgroundColor: COLORS.white,
    elevation: 4,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  listItemInner: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
  },
  listText: {
    fontSize: 18,
    fontWeight: '500',
    color: COLORS.black,
  },
  logo: {
    width: 45,
    height: 45,
    borderRadius: 25,
    backgroundColor: COLORS.secondaryDark,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoImg: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
  },
  icon: {
    marginTop: 4,
  },
  arrowContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#FCE7DE',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  divider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 0,
  },
});

export default DirectivesSection;
