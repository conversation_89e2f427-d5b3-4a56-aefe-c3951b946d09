/**
 * @format
 */

import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import firebase from '@react-native-firebase/app';
import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import axios from 'axios';
let isApiCallExecuted = false;

messaging().setBackgroundMessageHandler(async remoteMessage => {
  // Check if API call is already executed
  if (!isApiCallExecuted) {
    // Set flag to indicate that the API call is being executed
    isApiCallExecuted = true;

    try {
      // Retrieve the token from AsyncStorage
      const token = await AsyncStorage.getItem('accessToken');
      console.log('accessToken: ' + token);
      console.log('remote data count', remoteMessage.data.initialCount);
      if (token) {
        const payload = {
          alert_count: remoteMessage.data.initialCount, // Assuming initialCount is passed in the notification's data
        };
        // Make API call using Axios with the retrieved token
        const response = await axios.post(
          'https://punjabisamaj.phpteam.in/public/api/user-alert/read',
          payload,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          },
        );
        console.log('API response:', response.data);
      } else {
        throw new Error('Token not found in AsyncStorage');
      }
    } catch (error) {
      console.error('Error updating alert count:', error);
    }

    // Reset the flag after the API call is completed
    isApiCallExecuted = false;
  }
});
// Initialize Firebase with configuration
firebase.initializeApp();

AppRegistry.registerComponent(appName, () => App);
