import React, { useRef, useState } from 'react';
import { StyleSheet, View, Image, Alert, Pressable } from 'react-native';
import auth from '@react-native-firebase/auth';
import LOGO from '../../utils/image-constants';
import { hp, wp } from '../../utils/helper';
import Button from '../../components/atoms/buttons/Button';
import TextExtended from '../../components/atoms/texts/AppText';
import COLORS from '../../themes/colors';
import Routes from '../../navigation/RouteConstants';
import PhoneInput from 'react-native-phone-number-input';
import ROUTES from '../../navigation/RouteConstants';
import { useNavigation } from '@react-navigation/native';
import { Text } from 'react-native-render-html';

const Login = ({ navigation }) => {
  const [value, setValue] = useState('');
  const [loading, setLoading] = useState(false);
  const phoneInput = useRef<PhoneInput>(null);

  const handleGenerateOTP = async () => {
    if (value && value.length > 9) {  
      console.log('value==>', value);
      setLoading(true);
      try {
        const confirmation = await auth().verifyPhoneNumber(value).catch(() => {
          setLoading(false);
          return null
        });
        setLoading(false);
        if (!confirmation) return
         navigation.navigate(ROUTES.OTP_VERIFICATION, {
           phoneNumber: value,
           confirmation,
         });
      } catch (error) {
        console.log('Error sending confirmation code:', error);
        Alert.alert('Something went wrong while sending OTP');
        setLoading(false);
      }
    } else {
      Alert.alert('Please enter a valid 10-digit phone number');
      setLoading(false);
    }
  };
  const navigate = useNavigation();
  const handleNavigateGuestDashboard = () => {
    navigation.navigate(ROUTES.GUEST_DASHBOARD)
    console.log("loginGuestPressed")
  }
  return (
    <View style={styles.container}>
      <View>
        <Image source={LOGO} style={styles.logo} resizeMode="cover" />
      </View>

      <TextExtended style={styles.welcomeText} size={28}>
        Welcome!
      </TextExtended>

      <TextExtended style={styles.subheading} size={18}>
        Enter your mobile number to get started
      </TextExtended>

      {/* <CustomTextInput placeholder="Enter your mobile number" label="Mobile" /> */}

      <PhoneInput
        ref={phoneInput}
        defaultValue={value}
        defaultCode="IN"
        onChangeFormattedText={text => {
          setValue(text);
        }}
        withShadow
        autoFocus
        disableArrowIcon={false}
        containerStyle={styles.phoneContainer}
        textContainerStyle={styles.textContainer}
        textInputStyle={styles.textInput}
        flagButtonStyle={styles.flagButtonStyle}
      />

      {/* <TextExtended style={styles.termsText}>
        By continuing, I agree with{' '}
        <TextExtended style={{color: COLORS.primary}}>
          Terms & Conditions
        </TextExtended>
      </TextExtended> */}

      <View style={styles.btnContainer}>
        <Button
          title="GENERATE OTP"
          onPress={handleGenerateOTP}
          loading={loading}
        />
      </View>
      <TextExtended style={styles.subheading} size={18}>
        or
      </TextExtended>
      <Pressable onPress={handleNavigateGuestDashboard}>
        <TextExtended style={styles.guest}>Login as a Guest</TextExtended>

      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: wp(5),
    backgroundColor: COLORS.white,
  },

  logo: {
    marginTop: hp(7),
    width: wp(75),
    height: hp(30),
  },
  welcomeText: {
    fontWeight: 'bold',
    color: COLORS.text,
  },
  subheading: {
    marginTop: hp(1),
    marginBottom: hp(2),
    color: COLORS.textLight,
  },
  guest: {
    marginBottom: hp(2),
    color: COLORS.primary,
    fontSize: 20,
    fontWeight: 'bold'
  },
  termsText: {
    marginTop: hp(1.2),
    color: COLORS.textExtraLight,
    alignSelf: 'flex-start',
  },
  btnContainer: {
    width: '100%',
  },

  phoneContainer: {
    borderColor: 'transparent',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    width: '100%',
    alignSelf: 'center',
  },
  textContainer: {
    borderRadius: 5,
    backgroundColor: 'white',
  },
  textInput: {
    backgroundColor: 'white',
    color: 'black',
    fontSize: 14,
    paddingVertical: 0,
  },
  flagButtonStyle: {
    width: wp(15),
  },
});

export default Login;
