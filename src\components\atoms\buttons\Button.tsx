import React from 'react';
import {StyleSheet, Pressable, ActivityIndicator} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {hp} from '../../../utils/helper';
import TextExtended from '../texts/AppText';
import COLORS from '../../../themes/colors';

type ButtonProps = {
  onPress: () => void;
  title: string;
  loading: boolean;
};

const Button = ({onPress, title, loading}: ButtonProps) => {
  return (
    <Pressable onPress={onPress}>
      <LinearGradient
        colors={[COLORS.bgDarkShade, COLORS.bgLightShade]}
        style={styles.buttonGradient}
        start={{x: 1, y: 0}}
        end={{x: 0, y: 0}}>
        {loading ? (
          <ActivityIndicator size="small" color={COLORS.white} />
        ) : (
          <TextExtended style={styles.buttonText}>{title}</TextExtended>
        )}
      </LinearGradient>
    </Pressable>
  );
};

Button.defaultProps = {
  loading: false,
};

export default Button;

const styles = StyleSheet.create({
  buttonGradient: {
    marginTop: hp(5),
    borderRadius: 6,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },

  buttonText: {
    color: COLORS.white,
    fontWeight: 'bold',
    fontSize: 18,
    lineHeight: 30,
  },
});
