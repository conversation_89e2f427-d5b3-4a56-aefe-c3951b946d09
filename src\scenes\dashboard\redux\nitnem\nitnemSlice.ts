import {createSlice} from '@reduxjs/toolkit';

const nitnemSlice = createSlice({
  name: 'nitnem',
  initialState: {
    nitnemList: [],
    loading: false,
    error: null,
  },
  reducers: {
    fetchNitnemListRequest(state) {
      state.loading = true;
      state.error = null;
    },
    fetchNitnemListSuccess(state, action) {
      state.loading = false;
      state.nitnemList = action.payload;
    },
    fetchNitnemListFailure(state, action) {
      state.loading = false;
      state.error = action.payload;
    },
  },
});

export const {
  fetchNitnemListRequest,
  fetchNitnemListSuccess,
  fetchNitnemListFailure,
} = nitnemSlice.actions;

export default nitnemSlice.reducer;
