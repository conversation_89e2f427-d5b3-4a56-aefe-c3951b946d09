import auth, { firebase } from '@react-native-firebase/auth';
import { useRoute } from '@react-navigation/native';
import React, { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Alert, StyleSheet, View } from 'react-native';
import { TextInput, TouchableOpacity } from 'react-native-gesture-handler';
import TextExtended from '../../components/atoms/texts/AppText';
import ROUTES from '../../navigation/RouteConstants';
import COLORS from '../../themes/colors';
import { hp, wp } from '../../utils/helper';
import { useDispatch, useSelector } from 'react-redux';
import { LOG_ERROR, LOGIN } from './redux/AuthAction';
import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { userProfilePostFail } from './redux/AuthSlice';
const VerifyOTP = ({ navigation }) => {
  const [confirm, setConfirm] = useState(null);
  const [success, setSuccess] = useState(false);
  const [isOtpVerifing, setIsOtpVerifing] = useState(false);
  const [otpError, setOtpError] = useState('');
  const [fcmToken, setFcmToken] = useState('');
  const dispatch = useDispatch();
  const route = useRoute();
  const { isLoading, isLoggingIn } = useSelector(state => state.auth);
  const { phoneNumber, confirmation } = route.params;
  const otpArray = useRef(Array(6).fill('')).current; // Holds the OTP values
  const inputs = useRef([]);
  console.log('phone number', phoneNumber);
  const [focusedIndex, setFocusedIndex] = useState(null);
  useEffect(() => {
    console.log('isLoggingIn===>', isLoggingIn);
    if (isLoggingIn) {
      setIsOtpVerifing(false);
      dispatch(userProfilePostFail());
    }
  }, [isLoggingIn]);

  useEffect(() => {
    if (confirmation) {
      setConfirm(confirmation);
    }
  }, [confirmation]);

  const subscribeToTopic = async () => {
    try {
      await messaging().subscribeToTopic('allUsers');
      console.log('Subscribed to topic: allUsers');
    } catch (error) {
      console.error('Error subscribing to topic:', error);
    }
  };

  const setupFirebaseMessaging = async () => {
    try {
      const token = await messaging().getToken();
      setFcmToken(token);
      console.log('Firebase Messaging Token:', token);

      await AsyncStorage.setItem('fcmToken', token);
      messaging().onMessage(async remoteMessage => {
        console.log('Foreground Notification:', remoteMessage);
      });
    } catch (error) {
      console.error('Error setting up Firebase Messaging:', error);
    }
  };

  // const signInWithPhoneNumber = async () => {
  //   try {
  //     const confirmation = await auth().signInWithPhoneNumber(phoneNumber);
  //     setConfirm(confirmation);
  //   } catch (error) {
  //     console.log('Error sending confirmation code', error);
  //     Alert.alert('Something went wrong');
  //     navigation.navigate(ROUTES.LOGIN);
  //   }
  // };

  const handleOTPChange = (text, index) => {
    // Handle the case where the user is pasting OTP
    if (text.length > 1) {
      const splitText = text.split('');
      splitText.forEach((char, idx) => {
        if (idx < otpArray.length) {
          otpArray[idx] = char;
          inputs.current[idx]?.setNativeProps({ text: char });
          if (idx < otpArray.length - 1) {
            inputs.current[idx + 1]?.focus();
          }
        }
      });
    } else if (text.length === 1) {
      otpArray[index] = text;
      if (index < 5) {
        inputs.current[index + 1]?.focus();
      }
    }

    // If the OTP is completely filled, verify it
    if (otpArray.join('').length === 6 && confirm) {
      setIsOtpVerifing(true);
      confirmCode(confirm?.verificationId, otpArray.join(''));
    }
  };



  // const confirmCode = async code => {
  //   try {
  //     const userCredentials = await confirm.confirm(code);
  //     const idToken = await userCredentials.user.getIdToken();
  //     if (userCredentials) {
  //       // setSuccess(true);
  //       const payload = {
  //         mobile: phoneNumber,
  //         firebase_token: idToken,
  //         fcm_token: fcmToken,
  //       };
  //       await AsyncStorage.setItem('accessToken', idToken);

  //       dispatch({type: LOGIN, payload});
  //     }
  //   } catch (error) {
  //     console.log('Error confirming code', error);

  //     // payload for saga
  //     const payload = {
  //       text: `Error confirming OTP for phone number ${phoneNumber}. OTP: ${code}, Error: ${
  //         error.message || 'Unknown error'
  //       }`,
  //     };

  //     dispatch({type: LOG_ERROR, payload});

  //     setIsOtpVerifing(false);
  //     Alert.alert('Invalid OTP. Please try again.');
  //   }
  // };

  async function confirmCode(verificationId, code) {
    try {

      const credential = auth.PhoneAuthProvider.credential(
        verificationId,
        code,
      );


      const userCredential = await auth().signInWithCredential(credential);

      console.log('User Signed In:', userCredential.user);

      const idToken = await userCredential.user.getIdToken();

      const payload = {
        mobile: phoneNumber,
        firebase_token: idToken,
        fcm_token: fcmToken,
      };
      await AsyncStorage.setItem('accessToken', idToken);

      dispatch({ type: LOGIN, payload });
    } catch (error) {
      console.error('Error during sign-in:', error.message);
      setOtpError('Invalid OTP. Please try again.');
      const payload = {
        text: `Error confirming OTP for phone number ${phoneNumber}. OTP: ${code}, Error: ${error.message || 'Unknown error'
          }`,
      };

      dispatch({ type: LOG_ERROR, payload });
    }
    finally {
      setIsOtpVerifing(false); // Stop loading after OTP check
    }
  }

  useEffect(() => {
    // signInWithPhoneNumber();
    setupFirebaseMessaging();
    subscribeToTopic();
  }, []);


  const handleFocus = (index) => {
    setFocusedIndex(index);
  };

  const handleBlur = () => {
    setFocusedIndex(null);
  };

  const handleBackspace = (text, index) => {
    if (!text && index > 0) {
      inputs.current[index - 1]?.focus(); // Move to the previous field
    }
    otpArray[index] = ''; // Clear the value at the current index
  };
  return (
    <View style={styles.container}>
      {isLoading ? (
        <ActivityIndicator size="large" color={COLORS.primary} />
      ) : (
        <View style={styles.content}>
          {isOtpVerifing && (
            <ActivityIndicator size="large" color={COLORS.primary} />
          )}
          <TextExtended size={28} style={styles.heading}>
            Verification
          </TextExtended>
          <TextExtended style={styles.subheading} size={18}>
            {`Please enter the OTP that we sent to the Mobile number +91********${phoneNumber.slice(
              -2,
            )}`}
          </TextExtended>

          <View style={styles.otpContainer}>
            {otpArray.map((_, index) => (
              <TextInput
                key={index}
                ref={(ref) => (inputs.current[index] = ref)}
                style={[
                  styles.otpInput,
                  {
                    borderColor:
                      otpArray[index] || focusedIndex === index
                        ? COLORS.primary
                        : 'rgba(216, 216, 220, 0.91)',
                  },
                ]}
                keyboardType="numeric"
                maxLength={6}
                onChangeText={(text) => handleOTPChange(text, index)}
                onKeyPress={({ nativeEvent }) => {
                  if (nativeEvent.key === 'Backspace') {
                    handleBackspace(nativeEvent.text, index);
                  }
                }}
                onFocus={() => handleFocus(index)} // Set focus
                onBlur={handleBlur}
                autoFocus={index === 0}
                selectionColor={COLORS.primary}
              />
            ))}
          </View>
          {otpError ? (
            <TextExtended style={styles.errorText} size={16}>
              {otpError}
            </TextExtended>
          ) : null}
          {/* <TouchableOpacity onPress={() => console.log('resend otp')}>
            <TextExtended style={styles.resendOTP} size={16}>
              Didn't receive the OTP?{' '}
              <TextExtended style={{color: COLORS.primary}}>
                Resend OTP
              </TextExtended>
            </TextExtended>
          </TouchableOpacity> */}
        </View>
      )}
    </View>
  );
};

export default VerifyOTP;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    marginTop: hp(25),
    paddingHorizontal: wp(5),
  },

  heading: {
    fontWeight: 'bold',
    color: COLORS.text,
  },
  subheading: {
    marginTop: hp(1.8),
    marginBottom: hp(2),
    color: COLORS.textLight,
    textAlign: 'center',
  },
  content: {
    alignItems: 'center',
  },
  resendOTP: {
    marginTop: hp(1.2),
    color: COLORS.textLight,
  },

  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: hp(2),
    marginBottom: hp(1.8),
  },
  otpInput: {
    width: wp(11),
    height: hp(6),
    borderTopWidth: wp(0.2),
    borderRightWidth: wp(0.2),
    borderLeftWidth: wp(0.2),
    borderBottomWidth: wp(1.1),
    borderRadius: wp(3),
    marginHorizontal: wp(1.2),
    textAlign: 'center',
    fontSize: wp(6),
    backgroundColor: COLORS.secondary,
    color: COLORS.black
  },
  errorText: {
    color: 'red',
    marginTop: hp(1),
  },
});
