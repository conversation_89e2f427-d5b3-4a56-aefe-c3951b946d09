import { FlatList, Image, Pressable, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import CustomHeader from '../../components/molecules/CustomHeader';
import COLORS from '../../themes/colors';
import CustomSearchBar from '../../components/molecules/CustomSearchBar';
import { hp, wp } from '../../utils/helper';
import { CALLING_ICON, PROFILE_IMAGE } from '../../utils/image-constants';
import { useNavigation } from '@react-navigation/native';
const profileData = [
    {
        id: '1',
        firstName: 'Sushant',
        lastName: 'Batra',
        phoneNumber: '9876543210',
        location: 'Bilaspur, Chhattisgarh',
        image: PROFILE_IMAGE,
    },
    {
        id: '2',
        firstName: 'Amanpreet',
        lastName: 'Arora',
        phoneNumber: '9988776655',
        location: 'Delhi, India',
        image: PROFILE_IMAGE,
    },
    {
        id: '3',
        firstName: 'Manpreet',
        lastName: 'Bajwa',
        phoneNumber: '9988776655',
        location: 'Delhi, India',
        image: PROFILE_IMAGE,
    },
    {
        id: '4',
        firstName: 'Gurpreet',
        lastName: 'Gujar',
        phoneNumber: '9988776655',
        location: 'Delhi, India',
        image: PROFILE_IMAGE,
    },
    {
        id: '5',
        firstName: 'Aman',
        lastName: 'Batra',
        phoneNumber: '9988776655',
        location: 'Delhi, India',
        image: PROFILE_IMAGE,
    },
    {
        id: '6',
        firstName: 'Ratanjeet',
        lastName: 'jat',
        phoneNumber: '9988776655',
        location: 'Delhi, India',
        image: PROFILE_IMAGE,
    },
    {
        id: '7',
        firstName: 'Ratanjeet',
        lastName: 'jat',
        phoneNumber: '9988776655',
        location: 'Delhi, India',
        image: PROFILE_IMAGE,
    },

];
const PunjabiSamaj = () => {
    const navigation = useNavigation();
    const handleNavigateProfileDetail = () => {
        navigation.navigate('PROFILE_DETAIL');
    }
    return (
        <View style={styles.mainContainer}>
            <CustomHeader title="Punjabi Samaj" showBackButton={true} />
            <View style={styles.container}>
                <CustomSearchBar />

                <FlatList
                    data={profileData}
                    keyExtractor={(item) => item.id}
                    renderItem={({ item }) => (
                        <Pressable style={styles.profileCard} onPress={handleNavigateProfileDetail}>
                            <View style={styles.imageSide}>
                                <Image source={item.image} style={styles.profileImage} />
                            </View>
                            <View style={styles.bioSection}>
                                <Text style={styles.Name}>{item.firstName}</Text>
                                <Text style={styles.surname}>{item.lastName}</Text>
                                <View style={styles.callingSection}>
                                    <Image source={CALLING_ICON} style={styles.callingIcon} />
                                    <Text style={styles.number}>{item.phoneNumber}</Text>
                                </View>
                                <Text style={styles.location}>{item.location}</Text>
                            </View>
                        </Pressable>
                    )}
                />
            </View>

        </View>
    )
}

export default PunjabiSamaj;

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: COLORS.secondary,
    },
    container: {
        flex: 1,
        paddingVertical: hp(1.2),
        paddingHorizontal: wp(2.8),
        marginBottom: hp(4.5)
    },
    profileCard: {
        backgroundColor: COLORS.white,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        gap: 15,
        paddingHorizontal: wp(2.8),
        paddingVertical: hp(2),
        borderRadius: 5,
        elevation: 5,
        marginTop: hp(2),
    },
    imageSide: {
        width: '25%',
        alignItems: 'center',
        justifyContent: 'center',
        elevation: 5,
        backgroundColor: 'rgba(255, 251, 251, 0.1)',
        overflow: 'hidden',
        borderRadius: 10,

    },
    profileImage: {
        width: '100%',
        height: hp(10),
        borderRadius: 10,
        resizeMode: 'contain',

    },
    bioSection: {
        flex: 1,
    },
    Name: {
        fontSize: 20,
        fontWeight: '600',
        color: COLORS.black
    },
    surname: {
        fontSize: 12,
        fontWeight: '400',
    },
    callingSection: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        gap: 5,
    },
    callingIcon: {
        width: 15,
        height: 15,
    },
    number: {
        fontSize: 12,

    },
    location: {
        fontSize: 11,
        color: COLORS.black,
        fontWeight: '400',
    }

})