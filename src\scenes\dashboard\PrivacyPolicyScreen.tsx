import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  useWindowDimensions,
} from 'react-native';
import {PUNJABI_ICON} from '../../utils/image-constants';
import COLORS from '../../themes/colors';
import CustomHeader from '../../components/molecules/CustomHeader';
import he from 'he';
import {useSelector} from 'react-redux';
import RenderHTML from 'react-native-render-html';

const PrivacyPolicyScreen = () => {
  const {width} = useWindowDimensions();

  // Fetch settings data from Redux
  const {settings} = useSelector(state => state.settings);
  console.log('settings from contact us screen', settings?.privacy_policy);

  const encodedHTML = settings?.privacy_policy || {};
  // Decode the HTML entities (like &lt;, &gt;, etc.)
  const decodedHTML = he.decode(encodedHTML);

  const source = {
    html: decodedHTML,
  };

  return (
    <View style={styles.container}>
      <CustomHeader title="Privacy Policy" showBackButton={true} />

      {/* Logo */}
      <View style={styles.logoContainer}>
        <Image source={PUNJABI_ICON} style={styles.brandLogo} />
      </View>

      {/* Main Content */}
      <ScrollView
        contentContainerStyle={styles.contentWrapper}
        showsVerticalScrollIndicator={false}>
        <View style={styles.contentSection}>
          <Text style={styles.title}>Privacy Policy</Text>

          {/* Privacy Policy Content */}
          <View style={styles.section}>
            <RenderHTML contentWidth={width} source={source} />
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default PrivacyPolicyScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: 40,
    backgroundColor: COLORS.secondary,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 40,
  },
  brandLogo: {
    width: 150,
    height: 120,
    resizeMode: 'contain',
  },
  contentWrapper: {
    flexGrow: 1,
    paddingHorizontal: 20,
  },
  contentSection: {
    marginTop: 20,
  },
  title: {
    fontSize: 28,
    color: '#333',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  section: {
    marginBottom: 20,
  },
});
