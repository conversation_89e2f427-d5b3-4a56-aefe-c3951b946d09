import React from 'react';
import {View, ScrollView, StyleSheet, Text, Image} from 'react-native';
import RenderHTML from 'react-native-render-html';
import {useWindowDimensions} from 'react-native';
import {useSelector} from 'react-redux';
import CustomHeader from '../../components/molecules/CustomHeader';
import COLORS from '../../themes/colors';
import {PUNJABI_ICON} from '../../utils/image-constants';
import he from 'he';

const AboutUsScreen = () => {
  const {width} = useWindowDimensions();

  // Fetch settings data from Redux
  const {settings} = useSelector(state => state.settings);
  console.log('settings from contact us screen', settings?.about_us);

  const encodedHTML = settings?.about_us || '<p>No content available.</p>';
  // Decode the HTML entities (like &lt;, &gt;, etc.)
  const decodedHTML = he.decode(encodedHTML);

  const source = {
    html: decodedHTML || '<p>No content to display.</p>',
  };

  return (
    <View style={styles.container}>
      <CustomHeader title="About Us" showBackButton={true} />

      {/* Logo */}
      <View style={styles.logoContainer}>
        <Image source={PUNJABI_ICON} style={styles.brandLogo} />
      </View>

      {/* Main Content */}
      <ScrollView
        contentContainerStyle={styles.contentWrapper}
        showsVerticalScrollIndicator={false}>
        <View style={styles.contentSection}>
          {/* Privacy Policy Content */}
          <View style={styles.section}>
            <RenderHTML contentWidth={width} source={source} />
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: 40,
    backgroundColor: COLORS.secondary,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 40,
  },
  brandLogo: {
    width: 150,
    height: 120,
    resizeMode: 'contain',
  },
  contentWrapper: {
    flexGrow: 1,
    paddingHorizontal: 20,
  },
  contentSection: {},
  section: {
    marginBottom: 20,
  },
});

export default AboutUsScreen;
