import {createSlice} from '@reduxjs/toolkit';

const businessSlice = createSlice({
  name: 'business',
  initialState: {
    businesses: [],
    businessDetails: {},
    loading: false,
    isDetailsLoading: false,
    paginationLoading: false,
    error: null,
    total: null,
    allDataFetched: false,
  },
  reducers: {
    fetchBusinessesRequest(state, action) {
      state.error = null;

      if (action.payload.page === 1) {
        state.loading = true;
        state.paginationLoading = false;
      } else {
        state.paginationLoading = true;
      }
    },
    fetchBusinessesSuccess(state, action) {
      const {data, total, page} = action.payload;

      console.log('data from slice', data);

      if (page > 1) {
        // Using spread operator to append data to the existing businesses array
        state.businesses = [...state.businesses, ...data];
      } else {
        // Replacing businesses with the new data when it's the first page
        state.businesses = data;
      }

      state.total = total;

      if (page === 1) {
        state.loading = false;
      } else {
        state.paginationLoading = false;
      }

      // Check if all data has been fetched
      if (state.businesses.length >= total) {
        state.allDataFetched = true;
      }
    },
    fetchBusinessesFailure(state, action) {
      state.loading = false;
      state.error = action.payload;
      state.paginationLoading = false;
    },
    fetchBusinessDetailsRequest(state) {
      state.isDetailsLoading = true;
      state.error = null;
    },
    fetchBusinessDetailsSuccess(state, {payload}) {
      console.log('payload====>', payload);
      state.isDetailsLoading = false;
      state.businessDetails = payload.businessDetails;
    },
    fetchBusinessDetailsFailure(state, action) {
      state.isDetailsLoading = false;
      state.error = action.payload;
    },

    // New reducer to clear business details
    clearBusinessDetails(state) {
      state.businessDetails = {};
    },
  },
});

export const {
  fetchBusinessesRequest,
  fetchBusinessesSuccess,
  fetchBusinessesFailure,
  fetchBusinessDetailsRequest,
  fetchBusinessDetailsSuccess,
  fetchBusinessDetailsFailure,
  clearBusinessDetails,
} = businessSlice.actions;

export default businessSlice.reducer;
