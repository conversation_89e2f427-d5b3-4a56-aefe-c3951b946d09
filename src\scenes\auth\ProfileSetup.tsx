import React, {useEffect, useState} from 'react';
import {
  Image,
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  Text,
  Pressable,
} from 'react-native';
import {hp, wp} from '../../utils/helper';
import COLORS from '../../themes/colors';
import {
  AVATAR_DEFAULT,
  CAMERA_ICON,
  FEMALE_AVATAR,
  MALE_AVATAR,
  OTHER_AVATAR,
  RIGHT_ICON,
} from '../../utils/image-constants';
import Button from '../../components/atoms/buttons/Button';
import CustomTextInput from '../../components/atoms/inputs/CustomTextInput';
import TextExtended from '../../components/atoms/texts/AppText';
import {useNavigation} from '@react-navigation/native';
import CustomHeader from '../../components/molecules/CustomHeader';
import {useForm, Controller} from 'react-hook-form';
import DateTimePicker from '@react-native-community/datetimepicker';
import {useRoute} from '@react-navigation/native';
import {launchImageLibrary} from 'react-native-image-picker';
import {useSelector} from 'react-redux';
import {CustomDropdown} from '../../components/atoms/inputs/CustomDropdown';
import {store} from '../../store/store';
import AsyncStorage from '@react-native-async-storage/async-storage';
const CustomCircularCheckbox = ({
  selected,
  onPress,
  label,
}: {
  selected: boolean;
  onPress: () => void;
  label: string;
}) => (
  <TouchableOpacity style={styles.checkboxWrapper} onPress={onPress}>
    <View
      style={[
        styles.circularCheckbox,
        selected && styles.circularCheckboxSelected,
      ]}>
      {selected && <Image source={RIGHT_ICON} style={styles.checkIcon} />}
    </View>
    <TextExtended style={styles.checkboxLabel}>{label}</TextExtended>
  </TouchableOpacity>
);
const ProfileSetup = () => {
  const route = useRoute();
  const {firebase_token, phoneNumber} = route.params || {};
  console.log(
    'firebase token and phone from profile: ' +
      firebase_token +
      ' ' +
      phoneNumber,
  );

  const navigation = useNavigation();
  const [notificationStatus, setNotificationStatus] = useState(1);
  const {
    control,
    handleSubmit,
    formState: {errors},
    setValue,
    clearErrors,
    watch,
  } = useForm();

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [isDatePickerActive, setIsDatePickerActive] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [countryCode, setCountryCode] = useState('');
  const [countries, setCountries] = useState([]);
  const [selectedState, setSelectedState] = useState(null);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [selectedCity, setSelectedCity] = useState(null);

  const calculateAge = (dob: Date): string => {
    const today = new Date();
    const birthDate = new Date(dob);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }
    return age.toString();
  };

  const handleDateChange = (event: any, date?: Date) => {
    setShowDatePicker(false);
    if (date) {
      setSelectedDate(date);
      setIsDatePickerActive(false);
      if (date) {
        setSelectedDate(date);
        setValue('dob', date.toISOString().split('T')[0]);
      }

      const calculatedAge = calculateAge(date);
      setValue('age', calculatedAge);
      clearErrors('dob');
    }
  };
  const handleDatePickerFocus = () => {
    setIsDatePickerActive(true);
    setShowDatePicker(true);
  };

  // send plain object instead if form data
  const handleSubmitProfile = async (data: any) => {
    try {
      // Fetch the fcm_token from AsyncStorage
      const fcmToken = await AsyncStorage.getItem('fcmToken');

      if (fcmToken) {
        // Create a plain object to send data
        const profileData = {
          access_key: '8786',
          firebase_token: firebase_token,
          fcm_id: fcmToken,
          name: data.fullName,
          fathername: data.fathersName,
          email: data.email,
          spouse_name: data.spouseName,
          mobile: phoneNumber,
          whatsAppNo: data.whatsappNumber,
          dateOfBirth: data.dob,
          age: data.age,
          occupation: data.occupation,
          gender: data.gender,
          maritalStatus: data.maritalStatus,
          city: selectedCity?.label || selectedCity?.value || selectedCity,
          state: selectedState?.label || selectedState?.value || selectedState,
          country:
            selectedCountry?.label || selectedCountry?.value || selectedCountry,
          is_notification_visible: notificationStatus,
          is_number_visible: data.phoneNumberVisibility === 'Everyone' ? 1 : 0,
        };

        console.log('I am profile data:', profileData);

        // Assuming the API returns success, navigate to the next screen
        navigation.navigate('BUSINESS_DETAILS', {
          profileData: profileData,
          profileImage: profileImage,
        });
      } else {
        console.log('FCM token not found in AsyncStorage');
      }
    } catch (error) {
      console.error('Error fetching FCM token from AsyncStorage:', error);
    }
  };

  // select business image
  const handleImagePicker = () => {
    launchImageLibrary({mediaType: 'photo', quality: 0.8}, response => {
      if (response.didCancel) {
        console.log('User cancelled image picker');
      } else if (response.errorCode) {
        console.error('ImagePicker Error: ', response.errorMessage);
      } else {
        const imageUri = response.assets[0]?.uri;
        console.log('selected profile image', imageUri);
        setProfileImage(imageUri);
      }
    });
  };

  useEffect(() => {
    const getCountryList = async () => {
      try {
        const response = await fetch(
          'https://punjabisamaj.phpteam.in/public/api/get-location/country',
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              Authorization: 'Bearer ' + store.getState().auth.token,
            },
          },
        );
        const data = await response.json();
        const countryList = data.data.map(country => ({
          label: String(country.countryName),
          value: String(country.id),
          phoneCode: String(country.countryPhoneCode),
        }));
        setCountries(countryList);
      } catch (error) {
        console.error('Error fetching country list', error);
      }
    };
    getCountryList();
  }, []);
  // STATE
  useEffect(() => {
    const getStateList = async () => {
      try {
        const response = await fetch(
          `https://punjabisamaj.phpteam.in/public/api/get-location/state/${selectedCountry.value}`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              Authorization: 'Bearer ' + store.getState().auth.token,
            },
          },
        );
        const data = await response.json();
        const stateList = data.data.map(state => ({
          label: String(state.name),
          value: String(state.id),
        }));
        setStates(stateList);
      } catch (error) {
        console.error('Error fetching state list', error);
      }
    };
    if (selectedCountry?.value) {
      getStateList();
    }
  }, [selectedCountry]);

  // CITY
  useEffect(() => {
    const getCityList = async () => {
      try {
        const response = await fetch(
          `https://punjabisamaj.phpteam.in/public/api/get-location/city/${selectedState.value}`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              Authorization: 'Bearer ' + store.getState().auth.token,
            },
          },
        );
        const data = await response.json();
        const cityList = data.data.map(city => ({
          label: String(city.cityName),
          value: String(city.id),
        }));
        setCities(cityList);
      } catch (error) {
        console.error('Error fetching city list', error);
      }
    };
    if (selectedState?.value) {
      getCityList();
    }
  }, [selectedState]);

  // date
  const today = new Date();
  const maximumDate = new Date(today.setFullYear(today.getFullYear() - 13));
  const minimumDate = new Date(
    today.getFullYear() - 80,
    today.getMonth(),
    today.getDate(),
  );

  const selectedGender = watch('gender');

  return (
    <View style={styles.mainContainer}>
      <CustomHeader
        title="Dashboard"
        showBackButton={false}
        userName="Setup Profile"
      />

      {/* Form Fields */}
      <View style={styles.container}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="always">
          <View style={styles.imageContainer}>
            <TouchableOpacity onPress={handleImagePicker}>
              <Image
                style={styles.circularImage}
                // source={
                //   profileImage
                //     ? {uri: `data:image/jpeg;base64,${profileImage}`}
                //     : AVATAR_DEFAULT
                // }
                // source={profileImage ? {uri: profileImage} : FEMALE_AVATAR}
                source={
                  profileImage
                    ? {uri: profileImage}
                    : selectedGender === 'Male'
                    ? MALE_AVATAR
                    : selectedGender === 'Female'
                    ? FEMALE_AVATAR
                    : OTHER_AVATAR
                }
                resizeMode="cover"
              />
              <View style={styles.cameraIconContainer}>
                <Image source={CAMERA_ICON} style={styles.cameraIcon} />
              </View>
            </TouchableOpacity>
          </View>

          <View style={{padding: 5}}>
            <Controller
              control={control}
              name="fullName"
              rules={{required: 'Full Name is required'}}
              render={({field: {onChange, value}}) => (
                <CustomTextInput
                  placeholder="Full Name *"
                  label="Full Name"
                  value={value}
                  onChangeText={onChange}
                />
              )}
            />
            {errors.fullName && (
              <Text style={styles.errorText}>{errors.fullName.message}</Text>
            )}
            {/* Father's Name Input */}
            <Controller
              control={control}
              name="fathersName"
              rules={{required: "Father's Name is required"}}
              render={({field: {onChange, value}}) => (
                <CustomTextInput
                  placeholder="Father's Name *"
                  label="Father's Name"
                  value={value}
                  onChangeText={onChange}
                />
              )}
            />
            {errors.fathersName && (
              <Text style={styles.errorText}>{errors.fathersName.message}</Text>
            )}
            <Controller
              control={control}
              name="spouseName"
              // rules={{ required: 'Husband Name is required' }}
              render={({field: {onChange, value}}) => (
                <CustomTextInput
                  placeholder="Spouse's Name"
                  label="Spouse's Name"
                  value={value}
                  onChangeText={onChange}
                />
              )}
            />
            {/* {errors.husbandsName && <Text style={styles.errorText}>{errors.husbandsName.message}</Text>} */}
            <View style={styles.inputs}>
              {/* date picker */}
              <View style={styles.inputInner}>
                <Pressable
                  style={[
                    styles.datePickerButton,
                    isDatePickerActive && styles.activeDatePickerButton,
                  ]}
                  onPress={handleDatePickerFocus}>
                  <Text
                    style={[
                      styles.datePickerText,
                      selectedDate
                        ? styles.activeDatePickerText
                        : styles.inactiveDatePickerText,
                    ]}>
                    {selectedDate
                      ? selectedDate.toISOString().split('T')[0]
                      : 'Select Date of Birth'}
                  </Text>
                </Pressable>
                {errors.dob && (
                  <Text style={styles.errorText}>{errors.dob.message}</Text>
                )}
                {showDatePicker && (
                  <DateTimePicker
                    mode="date"
                    display="default"
                    value={selectedDate || new Date()}
                    onChange={handleDateChange}
                    maximumDate={maximumDate}
                    minimumDate={minimumDate}
                  />
                )}
              </View>

              {/* Age  */}
              <View style={styles.inputInnerAge}>
                <Controller
                  control={control}
                  name="age"
                  rules={{required: 'Date of Birth is required'}}
                  render={({field: {onChange, value}}) => (
                    <CustomTextInput
                      placeholder="Age *"
                      label="Age"
                      value={value}
                      onChangeText={onChange}
                      editable={false}
                    />
                  )}
                />
              </View>
            </View>
            {errors.age && (
              <Text style={styles.errorText}>{errors.age.message}</Text>
            )}

            <Controller
              control={control}
              name="occupation"
              rules={{required: 'Occupation is required'}}
              render={({field: {onChange, value}}) => (
                <CustomTextInput
                  placeholder="Occupation *"
                  label="Occupation"
                  value={value}
                  onChangeText={onChange}
                />
              )}
            />
            {errors.occupation && (
              <Text style={styles.errorText}>{errors.occupation.message}</Text>
            )}
            <Controller
              control={control}
              name="mobileNumber"
              // rules={{required: 'Mobile Number is required'}}
              render={({field: {onChange, value}}) => (
                <CustomTextInput
                  placeholder="Mobile Number *"
                  label="Mobile Number"
                  value={phoneNumber}
                  onChangeText={onChange}
                  keyboardType="numeric"
                  editable={false}
                />
              )}
            />
            {/* {errors.mobileNumber && (
              <Text style={styles.errorText}>
                {errors.mobileNumber.message}
              </Text>
            )} */}

            <Controller
              control={control}
              name="whatsappNumber"
              rules={{
                required: 'Whatsapp Number is required',
                pattern: {
                  value: /^[0-9]{10}$/,
                  message:
                    'Invalid Whatsapp number. It must be a 10-digit number.',
                },
              }}
              render={({field: {onChange, value}}) => (
                <CustomTextInput
                  placeholder="Whatsapp Number *"
                  label="Whatsapp Number"
                  value={value}
                  onChangeText={onChange}
                  keyboardType="numeric"
                />
              )}
            />
            {errors.whatsappNumber && (
              <Text style={styles.errorText}>
                {errors.whatsappNumber.message}
              </Text>
            )}

            {/* email */}
            <Controller
              control={control}
              name="email"
              rules={{
                pattern: {
                  value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                  message: 'Invalid email address.',
                },
                // required: {
                //   value: true,
                //   message: 'Email is required.',
                // },
              }}
              render={({field: {onChange, value}}) => (
                <CustomTextInput
                  placeholder="Email Address"
                  label="Email Address"
                  value={value}
                  onChangeText={onChange}
                  keyboardType="email-address"
                />
              )}
            />
            {errors.email && (
              <Text style={styles.errorText}>{errors.email.message}</Text>
            )}

            {/* Country and state*/}
            <View style={styles.dropdownSection}>
              <View style={{flex: 1}}>
                <CustomDropdown
                  control={control}
                  name="country"
                  placeholder="Country"
                  data={countries}
                  onChange={value => {
                    setSelectedCountry(value);
                    setSelectedState(null);
                    setValue('country', value);
                    setValue('state', {});
                    setValue('city', {});
                    setCities([]);
                    const selectedCountryObj = countries.find(
                      country => country.value === value.value,
                    );
                    setCountryCode(`+${selectedCountryObj?.phoneCode}` || '');
                    setValue(
                      'countryCode',
                      `+${selectedCountryObj?.phoneCode}` || '',
                    );
                    if (value) {
                      clearErrors('country');
                    }
                  }}
                  isSearch={true}
                  icon={'flag'}
                />
              </View>

              <View style={{flex: 1}}>
                <CustomDropdown
                  control={control}
                  name="state"
                  placeholder="State"
                  data={states}
                  onChange={value => {
                    setSelectedState(value);
                    setValue('state', value);
                    setValue('city', {});
                    if (value) {
                      clearErrors('state');
                    }
                  }}
                  disabled={!selectedCountry}
                  isSearch={true}
                  icon={'globe'}
                />
              </View>
            </View>
            {/* City */}
            <CustomDropdown
              control={control}
              name="city"
              placeholder="City"
              data={cities}
              onChange={(value: any) => {
                setSelectedCity(value);
                setValue('city', value);
                if (value) {
                  clearErrors('city');
                }
              }}
              disabled={!selectedState}
              isSearch={true}
              icon={'map-marker'}
            />
            <View style={styles.checkboxContainer}>
              <TextExtended style={styles.checkboxHead}>Gender</TextExtended>
              <View style={styles.checkbox}>
                <Controller
                  control={control}
                  name="gender"
                  rules={{required: 'Gender is required'}}
                  render={({field: {onChange, value}}) => (
                    <>
                      <View style={styles.checkboxRow}>
                        <CustomCircularCheckbox
                          selected={value === 'Male'}
                          onPress={() => onChange('Male')}
                          label="Male"
                        />
                      </View>
                      <View style={styles.checkboxRow}>
                        <CustomCircularCheckbox
                          selected={value === 'Female'}
                          onPress={() => onChange('Female')}
                          label="Female"
                        />
                      </View>
                      <View style={styles.checkboxRow}>
                        <CustomCircularCheckbox
                          selected={value === 'Other'}
                          onPress={() => onChange('Other')}
                          label="Other"
                        />
                      </View>
                    </>
                  )}
                />
              </View>
              {errors.gender && (
                <Text style={styles.errorText}>{errors.gender.message}</Text>
              )}
            </View>
            <View style={styles.checkboxContainer}>
              <TextExtended style={styles.checkboxHead}>
                Marital Status
              </TextExtended>
              <View style={styles.checkbox}>
                <Controller
                  control={control}
                  name="maritalStatus"
                  m
                  rules={{required: 'Marital Status is required'}}
                  render={({field: {onChange, value}}) => (
                    <>
                      <View style={styles.checkboxRow}>
                        <CustomCircularCheckbox
                          selected={value === 'Single'}
                          onPress={() => onChange('Single')}
                          label="Single"
                        />
                      </View>
                      <View style={styles.checkboxRow}>
                        <CustomCircularCheckbox
                          selected={value === 'Married'}
                          onPress={() => onChange('Married')}
                          label="Married"
                        />
                      </View>
                      <View style={styles.checkboxRow}>
                        <CustomCircularCheckbox
                          selected={value === 'Divorced'}
                          onPress={() => onChange('Divorced')}
                          label="Divorced"
                        />
                      </View>
                      <View style={styles.checkboxRow}>
                        <CustomCircularCheckbox
                          selected={value === 'Widowed'}
                          onPress={() => onChange('Widowed')}
                          label="Widowed"
                        />
                      </View>
                    </>
                  )}
                />
              </View>
              {errors.maritalStatus && (
                <Text style={styles.errorText}>
                  {errors.maritalStatus.message}
                </Text>
              )}
            </View>
            <View style={styles.checkboxContainer}>
              <TextExtended style={styles.checkboxHead}>
                Phone Number Visibility
              </TextExtended>
              <View style={styles.checkbox}>
                <Controller
                  control={control}
                  name="phoneNumberVisibility"
                  rules={{required: 'Phone number visibility is required'}}
                  render={({field: {onChange, value}}) => (
                    <>
                      <View style={styles.checkboxRow}>
                        <CustomCircularCheckbox
                          selected={value === 'Everyone'}
                          onPress={() => onChange('Everyone')}
                          label="Everyone"
                        />
                      </View>
                      <View style={styles.checkboxRow}>
                        <CustomCircularCheckbox
                          selected={value === 'Nobody'}
                          onPress={() => onChange('Nobody')}
                          label="Nobody"
                        />
                      </View>
                    </>
                  )}
                />
              </View>
              {errors.phoneNumberVisibility && (
                <Text style={styles.errorText}>
                  {errors.phoneNumberVisibility.message}
                </Text>
              )}
            </View>

            {/* Checkboxes for notification status */}
            <View style={styles.checkboxContainer}>
              <TextExtended style={styles.checkboxHead}>
                Your notification Status
              </TextExtended>
              <View style={styles.checkbox}>
                <View style={styles.checkboxRow}>
                  <CustomCircularCheckbox
                    selected={notificationStatus === 1}
                    onPress={() => setNotificationStatus(1)}
                    label="On"
                  />
                </View>
                <View style={styles.checkboxRow}>
                  <CustomCircularCheckbox
                    selected={notificationStatus === 0}
                    onPress={() => setNotificationStatus(0)}
                    label="Off"
                  />
                </View>
              </View>
            </View>
          </View>
        </ScrollView>
        <View style={styles.btnContainer}>
          <Button
            title="Proceed to Business Application"
            onPress={handleSubmit(handleSubmitProfile)}
          />
        </View>
      </View>
    </View>
  );
};

export default ProfileSetup;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.secondary,
    paddingHorizontal: wp(5),
  },
  imageContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: hp(2),
    position: 'relative',
  },
  circularImage: {
    width: wp(30),
    height: hp(15),
    borderRadius: 100,
    borderWidth: 1,
    borderColor: COLORS.imgBorder,
  },
  cameraIconContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: COLORS.primary,
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 5,
  },
  cameraIcon: {
    width: 20,
    height: 20,
    tintColor: 'white',
    resizeMode: 'contain',
  },
  inputs: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
  },
  inputInner: {
    flex: 3,
  },
  inputInnerAge: {
    flex: 1,
  },
  btnContainer: {
    width: '100%',
    marginBottom: hp(3),
  },

  checkboxHead: {
    fontSize: 15,
    fontWeight: '400',
    color: COLORS.black,
    marginBottom: 5,
  },

  checkboxContainer: {
    marginVertical: 10,
  },
  checkboxWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: hp(1),
  },

  checkbox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    flexWrap: 'wrap',
  },
  checkboxRow: {
    backgroundColor: COLORS.white,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    width: '33%',
    padding: 3,
    justifyContent: 'center',
    borderColor: COLORS.grayBorder,
    borderRadius: 5,
  },
  circularCheckbox: {
    width: 18,
    height: 18,
    borderRadius: 9,
    borderWidth: 2,
    borderColor: COLORS.bgDarkShade,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 5,
  },
  circularCheckboxSelected: {
    backgroundColor: COLORS.bgDarkShade,
  },
  checkIcon: {
    resizeMode: 'center',
    width: 10,
    height: 10,
  },
  checkboxLabel: {
    fontSize: 15,
    color: COLORS.black,
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 5,
  },
  datePickerButton: {
    flex: 1,
    marginTop: 20,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    borderRadius: 8,
    paddingHorizontal: 15,
    justifyContent: 'center',
    backgroundColor: COLORS.white,

    padding: 10,
    width: '100%',

    // // Shadow for iOS
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.8,
    shadowRadius: 4,
    // // Elevation for Android
    elevation: 5,
  },
  activeDatePickerButton: {
    borderColor: COLORS.primary,
  },
  datePickerText: {
    fontSize: 16,
    color: COLORS.textLight,
  },
  activeDatePickerText: {
    color: COLORS.black,
  },
  inactiveDatePickerText: {
    color: COLORS.textLight,
  },
  dropdownLabel: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
  },
  dropdown: {
    marginVertical: 10,
    borderWidth: 1,
    borderColor: 'transparent',
    borderRadius: 5,
    paddingHorizontal: 10,
    backgroundColor: COLORS.white,
    zIndex: 9,
  },
  dropdownContainer: {
    borderColor: '#ccc',
    borderRadius: 5,
  },
  dropdownSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
    marginTop: 5,
  },
});
