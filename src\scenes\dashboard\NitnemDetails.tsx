
import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Switch } from 'react-native';
import CustomHeader from '../../components/molecules/CustomHeader';
import COLORS from '../../themes/colors';

const NitnemDetails = ({ route }: any) => {
  const { header, details } = route.params;

  // State for controlling visibility of Hindi and English translations
  const [showHindi, setShowHindi] = useState(false);
  const [showEnglish, setShowEnglish] = useState(false);
  const shouldShowBorder = showHindi || showEnglish;
  return (
    <View style={styles.container}>
      {/* {/ Custom Header /} */}
      <CustomHeader title={header} showBackButton={true} />

      {/* {/ Content /} */}
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* {/ Composition Details /} */}
        {/* <Text style={styles.details}>
          Guru: {details.guru} | Raag: {details.raag} | Composition:{' '}
          {details.composition} | Source: {details.source} | Ang: {details.ang}
        </Text> */}

        {/* {/ Toggle Switches for Translations /} */}
        <View style={styles.toggleContainer}>
          <View style={styles.toggleItem}>
            <Text style={styles.toggleText}>Show Hindi </Text>
            <Switch
              value={showHindi}
              onValueChange={setShowHindi}
              thumbColor={showHindi ? COLORS.primary : COLORS.primary}
              trackColor={{ false: COLORS.grayBorder, true: COLORS.bgLightShade }}
            />
          </View>
          <View style={styles.toggleItem}>
            <Text style={styles.toggleText}>Show English </Text>
            <Switch
              value={showEnglish}
              onValueChange={setShowEnglish}
              thumbColor={showEnglish ? COLORS.primary : COLORS.primary}
              trackColor={{ false: COLORS.grayBorder, true: COLORS.bgLightShade }}
            />
          </View>
        </View>

        {/*  Render All Verses */}
        <View style={styles.versesContainer}>
          {details.verses.map((verse: any) => (
            <View key={verse.id} style={[
              styles.verseBlock,
              shouldShowBorder && styles.verseBlockBorder
            ]}>
              {/* Gurmukhi Verse  */}
              <Text style={styles.verseGurmukhi}>{verse.gurmukhi}</Text>

              {/* Conditionally Render Hindi Translation */}
              {showHindi && (
                <Text style={styles.verseHindi}>{verse.hindi}</Text>
              )}

              {/*  Roman Punjabi Transliteration  */}
              {showEnglish && (
                <Text style={styles.verseRomanPunjabi}>
                  {verse.roman_punjabi}
                </Text>
              )}

              {/* {/ Conditionally Render English Translation /} */}
              {showEnglish && (
                <Text style={styles.verseEnglish}>{verse.english}</Text>
              )}
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default NitnemDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  scrollContainer: {
    padding: 20,
    paddingBottom: 50,
  },
  details: {
    fontSize: 16,
    color: COLORS.black, // Light gray color for composition details
    textAlign: 'center',
    marginBottom: 30,
    // fontWeight: 'bold',
  },
  verseBlockBorder: {
    borderBottomWidth: 1,
    borderColor: 'rgb(236, 166, 138)'
  },
  toggleContainer: {
    marginBottom: 15,
    paddingHorizontal: 10,
    paddingVertical: 5,
    backgroundColor: COLORS.secondaryDark,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    alignItems: 'center',

  },
  toggleItem: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    backgroundColor: COLORS.secondary,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: COLORS.primary,
    flexBasis: '48%',
  },
  toggleText: {
    fontSize: 14,
    color: COLORS.primary,
    fontWeight: '500',
    letterSpacing: 0.4,
  },
  versesContainer: {
    // marginTop: 20,
  },
  verseBlock: {
    // marginBottom: 5,
    padding: 10,
    borderRadius: 10,
    // backgroundColor: COLORS.secondaryDark,
    // borderWidth: 1,
    // borderColor: COLORS.primary,
    shadowColor: '#ffffff',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 2,
    // borderBottomWidth: 1,
  },
  verseGurmukhi: {
    fontSize: 22,
    color: COLORS.bgDarkShade, // Golden color for Gurmukhi
    marginBottom: 5,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  verseHindi: {
    fontSize: 18,
    color: 'rgb(237, 152, 41)',
    marginBottom: 5,
    textAlign: 'center',
  },
  verseRomanPunjabi: {
    fontSize: 18,
    color: COLORS.black,
    marginBottom: 5,
    textAlign: 'center',
  },
  verseEnglish: {
    fontSize: 16,
    color: 'rgb(237, 152, 41)',
    textAlign: 'center',
  },
});
