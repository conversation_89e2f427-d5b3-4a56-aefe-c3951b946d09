import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  StatusBar,
  PermissionsAndroid,
  Platform,
  Linking,
  Alert,
} from 'react-native';
import AppNavigation from './src/navigation/AppNavigation';
import SplashScreen from 'react-native-splash-screen';
import COLORS from './src/themes/colors';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { persistedStore, store } from './src/store/store';
// test
import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import FlashMessage from 'react-native-flash-message';
import VersionCheck from 'react-native-version-check';
import { SafeAreaProvider } from 'react-native-safe-area-context';
const App = () => {
  useEffect(() => {
    setTimeout(() => {
      SplashScreen.hide();
    }, 500);

    requestNotificationPermission();
  }, []);

  const requestNotificationPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Notification permission granted');
        } else {
          console.log('Notification permission denied');
        }
      } catch (error) {
        console.error('Error requesting notification permission:', error);
      }
    }
  };

  useEffect(() => {
    const setupFirebaseMessaging = async () => {
      try {
        const token = await messaging().getToken();
        console.log('Firebase Messaging Token from app:', token);
        await AsyncStorage.setItem('fcmToken', token);
      } catch (error) {
        console.error('Error setting up Firebase Messaging:', error);
      }
    };
    setupFirebaseMessaging();
  }, []);
  useEffect(() => {

    // Check for app updates
    VersionCheck.needUpdate()
      .then(async res => {
        if (res.isNeeded) {
          Alert.alert(
            'Update Available',
            'A newer version of the app is available. Please update to continue.',
            [
              {
                text: 'Update Now',
                onPress: () => {
                  // Open app store/play store based on platform
                  Linking.openURL(res.storeUrl);
                },
              },
            ],
            { cancelable: false },
          );
        }
      })
      .catch(err => console.error('Version check error:', err));
  }, []);
  return (
    <Provider store={store}>
      <PersistGate persistor={persistedStore}>
        <View style={styles.container}>
          <StatusBar backgroundColor={COLORS.white} barStyle="dark-content" />
          <FlashMessage position="top" />
          <SafeAreaProvider>
          <AppNavigation />
          </SafeAreaProvider>
        </View>
      </PersistGate>
    </Provider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default App;
