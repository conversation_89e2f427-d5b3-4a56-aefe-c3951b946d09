import {combineReducers} from 'redux';
import authSlice from '../scenes/auth/redux/AuthSlice';
import notificationSlice from '../scenes/dashboard/redux/NotifictionSlice';
import notificationDetailSlice from '../scenes/dashboard/redux/NotificationDetailSlice';
import readNotificationSlice from '../scenes/dashboard/redux/ReadNotificationSlice';
import notificationCountSlice from '../scenes/dashboard/redux/NotificationCountSlice';
import increaseCountSlice from '../scenes/dashboard/redux/IncreaseCountSlice';
import businessSlice from '../scenes/dashboard/redux/business/businessSlice';
import settingsSlice from '../scenes/dashboard/redux/settings/settingsSlice';
import bannersSlice from '../scenes/dashboard/redux/banner/bannerSlice';
import nitnemSlice from '../scenes/dashboard/redux/nitnem/nitnemSlice';

const rootReducer = combineReducers({
  auth: authSlice,
  notification: notificationSlice,
  notificationDetail: notificationDetailSlice,
  readNotification: readNotificationSlice,
  notificationCount: notificationCountSlice,
  increaseCount: increaseCountSlice,
  business: businessSlice,
  settings: settingsSlice,
  banners: bannersSlice,
  nitnem: nitnemSlice,
});

export default rootReducer;
