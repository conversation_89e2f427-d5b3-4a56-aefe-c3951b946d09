import React, {useState} from 'react';
import {View, TextInput, StyleSheet, Appearance} from 'react-native';
import TextExtended from '../texts/AppText';
import COLORS from '../../../themes/colors';
const isDarkMode = Appearance.getColorScheme() === 'dark';

type CustomTextInputProps = {
  placeholder: string;
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  editable?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad'; // Optional prop for controlling editability
};

const CustomTextInput = ({
  placeholder,
  label,
  value,
  onChangeText,
  editable = true,
  keyboardType = 'default',
}: CustomTextInputProps) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => {
    if (editable) {
      setIsFocused(true);
    }
  };

  const handleBlur = () => {
    if (editable) {
      setIsFocused(false);
    }
  };

  return (
    <View style={styles.inputContainer}>
      <TextInput
        style={[
          styles.inputField,
          isFocused || value ? styles.inputFieldActive : null,
          !editable ? styles.inputFieldDisabled : null,
          {color: isDarkMode ? COLORS.black : COLORS.black},
        ]}
        onFocus={handleFocus}
        onBlur={handleBlur}
        value={value}
        onChangeText={onChangeText}
        editable={editable} // Pass editable to TextInput
        keyboardType={keyboardType}
      />
      <View
        pointerEvents="none"
        style={[
          styles.placeholderContainer,
          isFocused || value ? styles.placeholderContainerActive : null,
        ]}>
        <TextExtended
          style={[
            styles.placeholder,
            isFocused || value ? styles.placeholderActive : null,
            !editable ? styles.placeholderDisabled : null,
          ]}>
          {isFocused || value ? label : placeholder}
        </TextExtended>
      </View>
    </View>
  );
};

export default CustomTextInput;

const styles = StyleSheet.create({
  inputContainer: {
    width: '100%',
    position: 'relative',
    marginTop: 18,
  },
  inputField: {
    borderWidth: 1,
    borderColor: 'transparent',
    borderRadius: 7,
    padding: 10,
    width: '100%',
    backgroundColor: COLORS.white,
    elevation: 5,
  },
  inputFieldActive: {
    paddingTop: 20,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  inputFieldDisabled: {
    // backgroundColor: COLORS.white,
    // color: COLORS.darkGray,

    backgroundColor: COLORS.lightGray, // Light gray background for disabled state
    color: COLORS.darkGray, // Darker text color for disabled state
    borderColor: COLORS.lightGray, // Optional: remove border color for disabled state
    elevation: 0,
  },
  placeholder: {
    position: 'absolute',
    top: 12,
    left: 15,
    fontSize: 16,
    color: COLORS.textLight,
    zIndex: 1,
    backgroundColor: 'transparent',
  },
  placeholderActive: {
    top: -7,
    left: 20,
    fontSize: 12,
    zIndex: 1,
    borderRadius: 5,
    backgroundColor: COLORS.white,
    paddingHorizontal: 5,
    color: COLORS.primary,
  },
  placeholderDisabled: {
    color: COLORS.textLight,
  },

  // added styles for unresponsive text input tap
  placeholderContainer: {
    position: 'absolute',
    top: 0,
    zIndex: 1,
    backgroundColor: 'transparent',
    paddingHorizontal: 5,
  },
  placeholderContainerActive: {
    backgroundColor: COLORS.white,
    borderRadius: 5,
  },
});
