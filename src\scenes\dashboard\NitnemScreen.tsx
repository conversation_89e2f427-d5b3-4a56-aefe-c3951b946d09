import { StyleSheet, View, FlatList, Dimensions, Pressable } from 'react-native';
import React from 'react';
import COLORS from '../../themes/colors';
import CustomHeader from '../../components/molecules/CustomHeader';
import NitnemCard from '../../components/molecules/NitnemCard';
import { dummyApiResponse } from '../../data/scriptures';
import { useNavigation } from '@react-navigation/native';
import ROUTES from '../../navigation/RouteConstants';
import GuestCard from '../../components/molecules/GuestCard';
import * as Images from '../../utils/image-constants';
const NitnemScreen = () => {
    const navigation = useNavigation();

    const handleCardPress = (item: any) => {
        console.log('item', item);
        navigation.navigate(ROUTES.NITNEM_DETAILS, {
            header: item.type,
            details: item?.details,
        });
    };
    return (
        <View style={styles.mainContainer}>
            <CustomHeader title="Nitnem" showBackButton={true} />
            <FlatList
                data={dummyApiResponse.data}
                keyExtractor={(item, index) => `${item}-${index}`}
                contentContainerStyle={styles.container}
                renderItem={({ item }) => {
                    // Dynamically map the imageKey to the image constant
                    const image = Images[item.imageKey];

                    return (
                        <Pressable onPress={() => handleCardPress(item)}>
                            <GuestCard title={item.type} image={image} />
                        </Pressable>
                    );
                }}
                numColumns={2}
            />
        </View>
    );
};

export default NitnemScreen;

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: COLORS.secondary,

    },
    container: {
        marginVertical: 10,
        alignItems: 'flex-start',
        alignSelf: 'center',
        paddingBottom: 20,
    },

});
