import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    isLoading: false,
    increaseCount: {},

};

const IncreaseCountSlice = createSlice({
    name: 'increaseNotification',
    initialState,
    reducers: {
        increaseCountStarted: state => {
            state.isLoading = true;
        },

        increaseCountSuccess: (state, { payload }) => {

            state.isLoading = false;
            state.increaseCount = payload.data;
            state.isLoading = true
        },

        increaseCountFailed: state => {
            state.isLoading = false;

        },
    },
});

export const { increaseCountStarted, increaseCountSuccess, increaseCountFailed } =
    IncreaseCountSlice.actions;
export default IncreaseCountSlice.reducer;