import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    isLoading: false,
    notificationDetail: [],
};

const notificationDetailSlice = createSlice({
    name: 'notificationDetail',
    initialState,
    reducers: {
        notificationDetailStarted(state) {
            state.isLoading = true;
        },
        notificationDetailSuccess(state, { payload }) {
            state.isLoading = false;
            state.notificationDetail = payload.data;
        },
        notificationDetailFail(state) {
            state.isLoading = false;
        },
        clearNotificationDetails(state) {
            return initialState;
        }
    },
});

const { actions, reducer } = notificationDetailSlice;

export const {
    notificationDetailStarted,
    notificationDetailSuccess,
    notificationDetailFail,
    clearNotificationDetails
} = actions;
export default reducer;