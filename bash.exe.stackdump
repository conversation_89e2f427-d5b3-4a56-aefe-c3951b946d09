Stack trace:
Frame         Function      Args
0007FFFF8B70  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF7A70) msys-2.0.dll+0x2118E
0007FFFF8B70  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF8B70  0002100469F2 (00021028DF99, 0007FFFF8A28, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF8B70  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF8B70  00021006A545 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCC11A0000 ntdll.dll
7FFCBFC00000 KERNEL32.DLL
7FFCBE7D0000 KERNELBASE.dll
7FFCBF490000 USER32.dll
7FFCBE620000 win32u.dll
000210040000 msys-2.0.dll
7FFCBFD80000 GDI32.dll
7FFCBE4E0000 gdi32full.dll
7FFCBEBD0000 msvcp_win.dll
7FFCBE390000 ucrtbase.dll
7FFCC0760000 advapi32.dll
7FFCC0830000 msvcrt.dll
7FFCBFFD0000 sechost.dll
7FFCC08E0000 RPCRT4.dll
7FFCBD8C0000 CRYPTBASE.DLL
7FFCBEE00000 bcryptPrimitives.dll
7FFCBFBB0000 IMM32.DLL
