// import React, {useEffect, useState} from 'react';
// import {
//   Button,
//   FlatList,
//   Pressable,
//   StyleSheet,
//   Text,
//   TextInput,
//   View,
// } from 'react-native';
// import CustomHeader from '../../components/molecules/CustomHeader';
// import {useNavigation} from '@react-navigation/native';
// import {useDispatch, useSelector} from 'react-redux';
// import {FETCH_ALL_BUSINESSES} from './redux/business/businessAction';
// import COLORS from '../../themes/colors';
// import BusinessCard from '../../components/molecules/BusinessCard';

// const BusinessList = () => {
//   const [currentPage, setCurrentPage] = useState(1);
//   const [searchQuery, setSearchQuery] = useState('');
//   const navigation = useNavigation();
//   const dispatch = useDispatch();

//   // Access businesses data from Redux store
//   const {businesses, loading} = useSelector(store => store.business);

//   useEffect(() => {
//     // Trigger API call to fetch businesses when the component mounts
//     dispatch({type: FETCH_ALL_BUSINESSES});
//   }, [dispatch]);

//   const handleNavigateBusinessDetails = businessId => {
//     navigation.navigate('DETAILS_BUSINESS', {businessId});
//   };

//   const renderBusinessCard = ({item}) => (
//     <BusinessCard item={item} onPress={handleNavigateBusinessDetails} />
//   );

//   const handleSearch = () => {
//     console.log('inside handle search');
//     setCurrentPage(1);
//     dispatch({type: FETCH_ALL_BUSINESSES, payload: {searchQuery, page: 1}});
//   };

//   const handleRefresh = () => {
//     setCurrentPage(1);
//     dispatch({type: FETCH_ALL_BUSINESSES, payload: {searchQuery, page: 1}});
//   };

//   const loadMoreBusinesses = () => {
//     if (!loading) {
//       setCurrentPage(prevPage => prevPage + 1);
//       dispatch({
//         type: FETCH_ALL_BUSINESSES,
//         payload: {searchQuery, page: currentPage + 1},
//       });
//     }
//   };

//   return (
//     <View style={styles.mainContainer}>
//       <CustomHeader title="Business Directory" showBackButton={true} />

//       {/* Search Box */}
//       {businesses?.length > 0 && (
//         <View style={styles.searchBoxContainer}>
//           <TextInput
//             placeholder="Search Businesses"
//             value={searchQuery}
//             onChangeText={setSearchQuery}
//             style={styles.searchInput}
//           />
//           <Pressable
//             style={styles.searchButton}
//             onPress={() => {
//               console.log('Pressed Search'); // Debugging log
//               handleSearch();
//             }}>
//             <Text style={styles.searchButtonText}>Search</Text>
//           </Pressable>
//         </View>
//       )}

//       {/* Business List */}
//       {loading ? (
//         <Text style={styles.loadingText}>Loading...</Text>
//       ) : businesses?.length > 0 ? (
//         <FlatList
//           data={businesses}
//           renderItem={renderBusinessCard}
//           keyExtractor={item => item.id.toString()}
//           contentContainerStyle={styles.container}
//           refreshing={loading}
//           onRefresh={handleRefresh}
//         />
//       ) : (
//         <Text style={styles.emptyText}>No businesses found.</Text>
//       )}
//     </View>
//   );
// };

// export default BusinessList;

// const styles = StyleSheet.create({
//   mainContainer: {
//     flex: 1,
//     backgroundColor: COLORS.secondary,
//   },
//   container: {
//     padding: 10,
//   },

//   // Search Box
//   searchBoxContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     padding: 5,
//     marginHorizontal: 10,
//     marginBottom: 10,
//     borderRadius: 8,
//     backgroundColor: COLORS.white,
//     borderColor: COLORS.gray,
//     borderWidth: 1,
//     marginTop: 16,
//   },
//   searchInput: {
//     flex: 1,
//     padding: 8,
//     fontSize: 16,
//     color: COLORS.black,
//   },

//   searchButton: {
//     backgroundColor: COLORS.primary,
//     paddingVertical: 10,
//     paddingHorizontal: 15,
//     borderRadius: 8,
//     justifyContent: 'center',
//     alignItems: 'center',
//     marginLeft: 5,
//   },
//   searchButtonText: {
//     color: COLORS.white,
//     fontSize: 16,
//     fontWeight: 'bold',
//   },

//   shadowWrapper: {
//     flex: 1,
//     borderRadius: 12,
//     paddingVertical: 2,
//     paddingHorizontal: 1,
//   },
//   card: {
//     marginBottom: 10,
//     backgroundColor: COLORS.white,
//     padding: 10,
//     flexDirection: 'row',
//     gap: 10,
//     borderRadius: 10,
//     alignItems: 'center',
//     elevation: 6,
//   },
//   imageContainer: {
//     width: 50,
//     height: 50,
//   },
//   image: {
//     width: '100%',
//     height: '100%',
//     borderRadius: 8,
//   },
//   textSection: {
//     flex: 1,
//   },
//   headingText: {
//     fontSize: 16,
//     fontWeight: '600',
//     color: COLORS.black,
//   },
//   subText: {
//     fontSize: 12,
//     fontWeight: '600',
//     color: COLORS.black,
//   },
//   date: {
//     fontSize: 10,
//     fontWeight: '600',
//     color: COLORS.black,
//     alignSelf: 'flex-end',
//   },
//   loadingText: {
//     textAlign: 'center',
//     marginTop: 20,
//     fontSize: 16,
//     color: COLORS.black,
//   },
//   emptyText: {
//     textAlign: 'center',
//     marginTop: 20,
//     fontSize: 16,
//     color: COLORS.black,
//   },
// });

import React, { useEffect, useState, useCallback } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Pressable,
  StyleSheet,
  Text,
  TextInput,
  View,
} from 'react-native';
import CustomHeader from '../../components/molecules/CustomHeader';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { FETCH_ALL_BUSINESSES } from './redux/business/businessAction';
import COLORS from '../../themes/colors';
import BusinessCard from '../../components/molecules/BusinessCard';
import Icon from 'react-native-vector-icons/FontAwesome';
import { Dropdown } from 'react-native-element-dropdown';

const BusinessList = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortOption, setSortOption] = useState(null);
  const [filterOption, setFilterOption] = useState(null);
  const { businesses, loading, total, paginationLoading } = useSelector(
    state => state.business,
  );
  const dispatch = useDispatch();
  const navigation = useNavigation();

  console.log('current page: ' + currentPage, businesses);

  const sortData = [
    {
      label: 'A to Z',
      value: 'asc',
      icon: 'long-arrow-up',
    },
    {
      label: 'Z to A',
      value: 'desc',
      icon: 'long-arrow-down',
    },
    // {label: 'New Businesses', value: 'new'},
    // {label: 'Old Businesses', value: 'old'},
  ];

  const filterData = [
    { label: 'Food', value: 'food' },
    { label: 'Technology', value: 'technology' },
    { label: 'Health', value: 'health' },
    { label: 'Retail', value: 'retail' },
    { label: 'Spiritual', value: 'spiritual' },
    { label: 'Sports', value: 'sports' },
    { label: 'Travel', value: 'travel' },
  ];

  useFocusEffect(
    useCallback(() => {
      handleRefresh();
    }, [])
  );
  useEffect(() => {
    if (currentPage > 1) {
      dispatch({
        type: FETCH_ALL_BUSINESSES,
        payload: {
          searchQuery,
          page: currentPage,
          sortOption,
          filterOption,
        },
      });
    }
  }, [currentPage]);

  // Navigate to Details screen
  const handleNavigateBusinessDetails = businessId => {
    navigation.navigate('DETAILS_BUSINESS', { businessId });
  };

  // Handle Search
  const handleSearch = useCallback(() => {
    setCurrentPage(1);
    dispatch({ type: FETCH_ALL_BUSINESSES, payload: { searchQuery, page: 1 } });
  }, [dispatch, searchQuery]);

  // Handle Refresh
  const handleRefresh = () => {
    setCurrentPage(1);
    setFilterOption(null);
    setSortOption(null);
    setSearchQuery('');
    dispatch({ type: FETCH_ALL_BUSINESSES, payload: { searchQuery, page: 1 } });
  };

  // sort
  const handleSortChange = useCallback(
    (sortOption) => {
      setSortOption(sortOption);
      setCurrentPage(1); // Reset to page 1 when sorting changes
      dispatch({
        type: FETCH_ALL_BUSINESSES,
        payload: {
          order_by: sortOption,
          page: 1, // Always start from the first page after sorting
          searchQuery,
          filterOption,
        },
      });
    },
    [dispatch, searchQuery, filterOption]
  );

  const handleFilterChange = filterOption => {
    console.log('filtered', filterOption);
    dispatch({
      type: FETCH_ALL_BUSINESSES,
      payload: {
        category: filterOption,
      },
    });
  };
  const handleLoadMore = () => {
    const totalPages = Math.ceil(total / 10); // Adjust `10` based on items per page
    if (currentPage < totalPages && !loading && !paginationLoading) {
      setCurrentPage((prevPage) => prevPage + 1);
    }
  };
  // Render Buisness Card
  const renderBusinessCard = ({ item }) => (
    <BusinessCard item={item} onPress={handleNavigateBusinessDetails} />
  );

  return (
    <View style={styles.mainContainer}>
      <CustomHeader title="Business Directory" showBackButton={true} />

      {/* Search Box */}
      <View style={styles.searchBoxContainer}>
        <TextInput
          placeholder="Search Business..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={styles.searchInput}
          placeholderTextColor={COLORS.gray}
        />
        <Pressable style={styles.searchButton} onPress={handleSearch}>
          <Icon name="search" color={COLORS.white} size={20} />
        </Pressable>
      </View>

      {/* Sort and Filter */}
      <View style={styles.sortFilterContainer}>
        {/* Sort Dropdown */}
        <View style={styles.dropdownContainer}>
          <Dropdown
            data={sortData}
            labelField="label"
            valueField="value"
            placeholder="Select Sort Option"
            value={sortOption}
            onChange={item => {
              setSortOption(item.value);
              handleSortChange(item.value);
            }}
            style={styles.dropdown}
            placeholderStyle={styles.placeholderStyle}
            selectedTextStyle={styles.selectedTextStyle}
            renderItem={item => (
              <View style={styles.dropdownItem}>
                <Icon name={item.icon} color={COLORS.primary} size={20} />
                <Text style={styles.dropdownLabel}>{item.label}</Text>
              </View>
            )}
          />
        </View>

        {/* Filter Dropdown */}
        <View style={styles.dropdownContainer}>
          <Dropdown
            data={filterData}
            labelField="label"
            valueField="value"
            placeholder="Select Filter Option"
            value={filterOption}
            onChange={item => {
              setFilterOption(item.value);
              handleFilterChange(item.value);
            }}
            style={styles.dropdown}
            placeholderStyle={styles.placeholderStyle}
            selectedTextStyle={styles.selectedTextStyle}
            renderItem={item => (
              <View style={styles.dropdownItem}>
                <Text style={styles.dropdownLabel}>{item.label}</Text>
              </View>
            )}
          />
        </View>
      </View>

      {/* Display count */}
      {!loading && total > 0 && (
        <View style={styles.countContainer}>
          <Text style={styles.totalCountLabel}>Available Businesses:</Text>
          <Text style={styles.totalCount}>
            {Math.min(businesses.length, total)} / {total}
          </Text>
        </View>
      )}

      {/* Business List */}
      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loaderText}>Loading...</Text>
        </View>
      ) : businesses?.length > 0 ? (
        <FlatList
          data={businesses}
          renderItem={renderBusinessCard}
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={styles.container}
          refreshing={loading}
          onRefresh={handleRefresh}
          // pagination
          onEndReached={handleLoadMore}

          // onEndReachedThreshold={1}
          ListFooterComponent={
            paginationLoading ? (
              <ActivityIndicator size="small" color={COLORS.primary} />
            ) : null
          }
        />
      ) : (
        <Text style={styles.emptyText}>No businesses found.</Text>
      )}
    </View>
  );
};

export default BusinessList;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  container: {
    padding: 10,
  },

  // Search Box
  searchBoxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 5,
    marginHorizontal: 16,
    marginBottom: 12,
    marginTop: 20,
    backgroundColor: COLORS.white,
    borderRadius: 15,
    elevation: 5,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
  },

  searchInput: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    color: COLORS.black,
    borderRadius: 10,
    backgroundColor: COLORS.lightGray,
  },

  searchButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
    elevation: 4,
  },

  // Loader and Empty Text
  loadingText: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
    color: COLORS.black,
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
    color: COLORS.black,
  },

  // Sort and Filter
  sortFilterContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    paddingHorizontal: 10,
  },
  dropdownContainer: {
    flex: 1,
    marginHorizontal: 5,
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: 8,
    elevation: 2,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  label: {
    fontSize: 14,
    color: COLORS.gray,
    marginBottom: 5,
  },
  dropdown: {
    borderWidth: 0,
    backgroundColor: COLORS.white,
    borderRadius: 8,
  },

  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  dropdownLabel: {
    marginLeft: 10,
    fontSize: 16,
    color: COLORS.black,
  },

  placeholderStyle: {
    fontSize: 14,
    color: COLORS.gray,
  },
  selectedTextStyle: {
    fontSize: 14,
    color: COLORS.black,
  },

  // Count
  countContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    marginHorizontal: 10,
  },
  totalCountLabel: {
    fontSize: 16,
    color: COLORS.darkGray,
    fontWeight: '600',
  },
  totalCount: {
    fontSize: 20,
    color: '#25D366',
    fontWeight: 'bold',
  },

  // loader
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
  },
  loaderText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
  },
});
