import React from 'react';
import {Controller} from 'react-hook-form';
import {Image, StyleSheet, Text, View} from 'react-native';
import {Dropdown} from 'react-native-element-dropdown';
import COLORS from '../../../themes/colors';
import FontAwesome from 'react-native-vector-icons/FontAwesome';

export const CustomDropdown = ({
  control,
  name,
  placeholder,
  data,
  icon,
  onChange,
  disabled = false,
  isSearch = false,
  rules = {},
}) => {
  const renderItem = (item, isSelected) => (
    <View style={[styles.itemContainer]}>
      <Text style={{color: isSelected ? '#000000' : '#000000'}}>
        {item.label}
      </Text>
    </View>
  );

  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      render={({
        field: {value, onChange: controllerOnChange},
        fieldState: {error},
      }) => (
        <View style={styles.dropdownWrapper}>
          <Dropdown
            style={[styles.dropdown, error && styles.dropdownError]}
            iconStyle={styles.iconStyle}
            placeholderStyle={styles.placeholderStyle}
            selectedTextStyle={styles.selectedTextStyle}
            containerStyle={styles.dropdownContainerStyle}
            keyboardAvoiding={true}
            data={data}
            labelField="label"
            valueField="value"
            placeholder={placeholder}
            value={value}
            onChange={item => {
              controllerOnChange(item.value);
              if (onChange) onChange(item);
            }}
            // renderLeftIcon={() => (
            //   <Image source={icon} style={styles.leftIconStyle} />
            // )}
            renderLeftIcon={() => (
              <FontAwesome
                name={icon} // Use the icon name passed as a prop
                size={22} // You can customize the size here
                color={COLORS.black} // Customize the color as needed
                style={styles.leftIconStyle}
              />
            )}
            renderItem={renderItem}
            disable={disabled}
            search={isSearch}
            inputSearchStyle={styles.searchStyle}
            searchPlaceholder="Search..."
          />
          {error && (
            <Text style={styles.errorText}>{error.message || 'Required'}</Text>
          )}
        </View>
      )}
    />
  );
};

const styles = StyleSheet.create({
  dropdownWrapper: {
    marginTop: 18,
  },
  dropdown: {
    // marginVertical: 10,
    borderColor: COLORS.white,
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 10,
    height: 50,
    backgroundColor: COLORS.white,
    elevation: 5,
  },
  dropdownContainerStyle: {
    overflow: 'hidden',
    backgroundColor: COLORS.white,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    borderWidth: 0,
    marginTop: 5,
  },
  itemContainer: {
    padding: 10,
    backgroundColor: COLORS.white,
  },
  selectedTextStyle: {
    color: COLORS.textLight,
    fontSize: 14,
  },
  placeholderStyle: {
    color: COLORS.black,
    fontSize: 14,
  },
  leftIconStyle: {
    width: 22,
    height: 22,
    // resizeMode: 'contain',
    marginRight: 10,
  },
  iconStyle: {
    width: 24,
    height: 24,
  },
  dropdownError: {
    borderColor: 'red',
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 2,
    marginBottom: 0,
  },
  searchStyle: {
    borderRadius: 5,
    padding: 0,
    height: 35,

    fontSize: 14,
    borderColor: 'transparent',
    color: COLORS.black,
  },
});
