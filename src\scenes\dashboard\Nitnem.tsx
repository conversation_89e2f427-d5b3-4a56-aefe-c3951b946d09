import React from 'react';
import {
  FlatList,
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import NitnemCard from '../../components/molecules/NitnemCard';
import COLORS from '../../themes/colors';
import {useNavigation} from '@react-navigation/native';
import ROUTES from '../../navigation/RouteConstants';
import {useSelector} from 'react-redux';
import {dummyApiResponse} from '../../data/scriptures';
import * as Images from '../../utils/image-constants';

const Nitnem = () => {
  const {nitnemList} = useSelector((state: any) => state.nitnem);
  console.log('Nitnem List', nitnemList);
  const navigation = useNavigation();

  // Navigate to view all page
  const handleViewAll = () => {
    navigation.navigate(ROUTES.NITNEM);
  };

  // Navigate to NitnemDetails with the selected item
  const handleCardPress = (item: any) => {
    console.log('item', item);
    navigation.navigate(ROUTES.NITNEM_DETAILS, {
      header: item.type,
      details: item?.details,
    });
  };

  // Nitnem Card
  const renderItem = ({item}: {item: any}) => {
    // Dynamically map the imageKey to the image constant from Images
    const image = Images[item.imageKey];

    return (
      <Pressable onPress={() => handleCardPress(item)}>
        <NitnemCard title={item?.type} image={image} />
      </Pressable>
    );
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.headerContainer}>
        <Text style={styles.nitnemText}>Nitnem</Text>
        <TouchableOpacity onPress={handleViewAll}>
          <Text style={styles.viewAllText}>View All</Text>
        </TouchableOpacity>
      </View>
      <FlatList
        data={dummyApiResponse?.data}
        renderItem={renderItem}
        keyExtractor={(item, index) => `${item}-${index}`}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );
};

export default Nitnem;

const styles = StyleSheet.create({
  mainContainer: {
    marginTop: 15,
    paddingBottom: 10,
    borderRadius: 12,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  nitnemText: {
    fontSize: 22,
    fontWeight: '600',
    color: COLORS.black,
  },
  viewAllText: {
    fontSize: 12,
    fontWeight: '500',
    color: COLORS.primary,
  },
  listContainer: {
    paddingVertical: 10,
  },
  separator: {
    width: 0,
  },
});
