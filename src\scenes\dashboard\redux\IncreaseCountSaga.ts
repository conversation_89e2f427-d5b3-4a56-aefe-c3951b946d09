import { call, put } from 'redux-saga/effects';
import { API_URL } from '../../../services/webConstants';
import { apiClient } from '../../../services/httpServices';
import { ALL_NOTIFICATIONS } from './NotficationAction';
import { increaseCountFailed, increaseCountStarted, increaseCountSuccess } from './IncreaseCountSlice';
import { NOTIFICATIONS_COUNT } from './NotificationCountAction';


function* increaseCountSaga(action) {
    const { payload } = action
    try {
        yield put(increaseCountStarted());
        yield call(
            apiClient.post,
            API_URL.INCREASE_COUNT,
            payload
        );
        yield put({ type: NOTIFICATIONS_COUNT })
    } catch (error) {
        yield put(increaseCountFailed());
    }
}

export default increaseCountSaga;