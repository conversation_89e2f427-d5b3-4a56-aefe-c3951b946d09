import {useNavigation, useRoute} from '@react-navigation/native';
import React from 'react';
import {Image, Platform, Pressable, StyleSheet, View} from 'react-native';
import PagerView from 'react-native-pager-view';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/FontAwesome';

const CarouselImageView = () => {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const route = useRoute();
  const {id, carouselImages} = route.params;
  const initialIndex = carouselImages.findIndex(item => item.id === id);
  const buttonPlaceTop = Platform.OS === 'ios' ? 20 + insets.top : 20;

  return (
    <View style={[styles.container, {paddingTop: insets.top}]}>
      <Pressable
        onPress={() => navigation.goBack()}
        style={{
          position: 'absolute',
          top: buttonPlaceTop,
          left: 20,
          zIndex: 1,
        }}>
        <Icon name="close" size={24} color="white" />
      </Pressable>
      <PagerView style={styles.pagerView} initialPage={initialIndex}>
        {carouselImages.map(({id, image_url}) => (
          <View key={id} style={styles.imageContainer}>
            <Image source={{uri: image_url}} style={styles.fullImage} />
          </View>
        ))}
      </PagerView>
    </View>
  );
};

export default CarouselImageView;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  pagerView: {
    flex: 1,
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    left: 20,
    zIndex: 1,
  },
  closeIcon: {
    width: 20,
    height: 20,
  },
});
