import React, { useEffect } from 'react';
import { Image, StyleSheet, View, Dimensions, Pressable } from 'react-native';
import CustomHeader from '../../components/molecules/CustomHeader';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import NotificationCard from '../../components/molecules/NotificationCard';
import { useNavigation } from '@react-navigation/native';
import COLORS from '../../themes/colors';
import { useDispatch, useSelector } from 'react-redux';
import ROUTES from '../../navigation/RouteConstants';
import { setNotificationId } from '../auth/redux/AuthSlice';
import { NOTIFICATIONS_COUNT } from './redux/NotificationCountAction';
import { selectNotificationCount } from '../../store/selectors';
import DirectivesSection from '../../components/molecules/Directives';
import SwiperFlatList from 'react-native-swiper-flatlist';
import { FETCH_BANNER_DATA } from './redux/banner/bannerAction';
import Nitnem from './Nitnem';
import { FETCH_NITNEM } from './redux/nitnem/nitnemAction';
import NitnemCard from '../../components/molecules/NitnemCard';
import { dummyApiResponse } from '../../data/scriptures';
import GuestHeader from '../../components/molecules/GuestHeader';
import GuestCard from '../../components/molecules/GuestCard';

import * as Images from '../../utils/image-constants';
const { width } = Dimensions.get('window');

const GuestDashboard = () => {
    const dispatch = useDispatch();
    // fetch Banner data
    useEffect(() => {
        // fetch Nitnem
        dispatch({ type: FETCH_NITNEM });
    }, []);
    const navigation = useNavigation();
    const handleCardPress = (item: any) => {
        console.log('item', item);
        navigation.navigate(ROUTES.NITNEM_DETAILS, {
            header: item.type,
            details: item?.details,
        });
    };
    return (
        <View style={styles.mainContainer}>

            <GuestHeader title='hello Guest' />

            <View style={styles.containerInner}>

                <FlatList
                    data={dummyApiResponse.data}
                    keyExtractor={(item, index) => `${item}-${index}`}
                    contentContainerStyle={styles.container}
                    renderItem={({ item }) => {
                        // Dynamically map the imageKey to the image constant
                        const image = Images[item.imageKey];

                        return (
                            <Pressable onPress={() => handleCardPress(item)}>
                                <GuestCard title={item.type} image={image} />
                            </Pressable>
                        );
                    }}
                    numColumns={2}
                    scrollEnabled
                />
            </View>

        </View>
    );
};

export default GuestDashboard;

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: COLORS.secondary,
        position: 'relative',
    },

    containerInner: {
        paddingHorizontal: 4,
        position: 'absolute',
        top: 110,
        bottom: 0,
        left: 0,
        right: 0,
        flex: 1,
        marginBottom: 20,
    }
});
