import React, {useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  Pressable,
  ScrollView,
  Linking,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import COLORS from '../../themes/colors';
import {useDispatch, useSelector} from 'react-redux';
import {FETCH_BUSINESS_BY_ID} from './redux/business/businessAction';
import CustomHeader from '../../components/molecules/CustomHeader';
import moment from 'moment';
import {clearBusinessDetails} from './redux/business/businessSlice';
import ROUTES from '../../navigation/RouteConstants';

const DetailsBusiness = ({route, navigation}) => {
  const dispatch = useDispatch();
  const {businessId} = route.params;

  // Access businesses data from Redux store
  const {businessDetails, isDetailsLoading: loading} = useSelector(
    store => store.business,
  );
  console.log('business details ====> ', businessDetails);

  useEffect(() => {
    console.log('inside use effect business');
    dispatch(clearBusinessDetails());
    const payload = {id: businessId};
    dispatch({type: FETCH_BUSINESS_BY_ID, payload});
  }, [dispatch, businessId]);

  // handler functions
  // Calcuklate Business duration handler fuction
  const calculateYearsInBusiness = business_established_year => {
    if (!business_established_year) return 'N/A';
    const currentDate = moment();
    const businessDate = moment(business_established_year);
    const yearsInBusiness = currentDate.diff(businessDate, 'years');
    return yearsInBusiness > 0
      ? `${yearsInBusiness} years in business`
      : 'Less than a year in business';
  };

  // WhatsApp handler function
  const handleWhatsApp = () => {
    const phoneNumber = businessDetails?.business_contact || '1234567890';
    const url = `https://wa.me/${phoneNumber}`;
    Linking.openURL(url).catch(err =>
      console.error('Error opening WhatsApp:', err),
    );
  };

  // Call handler function
  const handleCall = () => {
    const phoneNumber = `tel:${
      businessDetails?.business_whatsapp || '1234567890'
    }`;
    Linking.openURL(phoneNumber).catch(err =>
      console.error('Error making call:', err),
    );
  };

  // Email handler function
  const handleEmail = () => {
    const email =
      businessDetails?.business_email || '<EMAIL>';
    const url = `mailto:${email}`;
    Linking.openURL(url).catch(err =>
      console.error('Error opening email client:', err),
    );
  };

  return (
    <View style={styles.mainContainer}>
      <CustomHeader title="Business Details" showBackButton={true} />

      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loaderText}>Loading...</Text>
        </View>
      ) : (
        <ScrollView contentContainerStyle={styles.container}>
          {/* Image and Category Badge */}
          <View style={styles.imageContainer}>
            <Pressable
              onPress={() => {
                navigation.navigate(ROUTES.CAROUSEL_IMAGE_VIEW, {
                  id: businessDetails?.id,
                  carouselImages: [
                    {
                      id: businessDetails?.id,
                      image_url: businessDetails?.business_image,
                    },
                  ],
                });
              }}>
              <Image
                source={{
                  uri:
                    businessDetails?.business_image ||
                    'https://via.placeholder.com/600x300',
                }}
                style={styles.image}
              />
            </Pressable>
            <View style={styles.categoryBadge}>
              <Text style={styles.categoryText}>
                {businessDetails?.category}
              </Text>
            </View>
          </View>

          {/* Details Section */}
          <View style={styles.detailsSection}>
            {/* General Information Section */}
            <View>
              <Text style={styles.headingText}>
                {businessDetails?.business_name}
              </Text>
              <Text style={styles.subText}>
                {businessDetails?.business_info}
              </Text>

              <Text style={styles.addressText}>
                {businessDetails?.business_address || 'Address goes here'}
              </Text>
              <Text style={styles.yearsInBusiness}>
                {calculateYearsInBusiness(
                  businessDetails?.business_established_year,
                )}
              </Text>
            </View>

            {/* Location Section */}
            <View>
              <Text style={styles.inputHeading}>Business Location</Text>

              {/* City */}
              <View style={styles.locationField}>
                <Icon name="map-marker" size={20} color={COLORS.primary} />
                <Text style={styles.locationText}>
                  {businessDetails?.business_city || 'City goes here'}
                </Text>
              </View>

              {/* State */}
              <View style={styles.locationField}>
                <Icon name="globe" size={20} color="#007AFF" />
                <Text style={styles.locationText}>
                  {businessDetails?.business_state || 'State goes here'}
                </Text>
              </View>

              {/* Country */}
              <View style={styles.locationField}>
                <Icon name="flag" size={20} color="#007AFF" />
                <Text style={styles.locationText}>
                  {businessDetails?.business_country || 'Country goes here'}
                </Text>
              </View>
            </View>

            {/* Contact Section */}
            <View>
              <Text style={styles.contactHeading}>Contact Information</Text>
              <View style={styles.contactSection}>
                {/* Call Now */}
                <Pressable onPress={handleCall} style={styles.iconWrapper}>
                  <Icon name="phone" size={24} color="#007AFF" />
                  <Text style={styles.actionText}>Call Now</Text>
                </Pressable>

                {/* Chat (WhatsApp) */}
                <Pressable onPress={handleWhatsApp} style={styles.iconWrapper}>
                  <Icon name="whatsapp" size={24} color="#25D366" />
                  <Text style={styles.actionText}>Chat</Text>
                </Pressable>

                {/* Email */}
                <Pressable onPress={handleEmail} style={styles.iconWrapper}>
                  <Icon name="envelope-o" size={22} color="#000" />
                  <Text style={styles.actionText}>Email</Text>
                </Pressable>
              </View>
            </View>

            {/* Additional Details Section */}
            {/* <Text style={styles.additionalDetailsText}>
            Additional Information goes here if needed.
          </Text> */}
          </View>
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: COLORS.secondary,
  },
  container: {
    flexGrow: 1,
    padding: 16,
  },

  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
  },
  loaderText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
  },
  // imageContainer: {
  //   position: 'relative',
  // },
  // image: {
  //   width: '100%',
  //   height: 300,
  //   borderRadius: 8,
  // },

  // new test
  imageContainer: {
    position: 'relative',
    width: '100%',
    aspectRatio: 16 / 9, // Set container to maintain 16:9 aspect ratio
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    resizeMode: 'cover', // Ensure the image covers the container
  },

  categoryBadge: {
    position: 'absolute',
    top: 16,
    left: 16,
    backgroundColor: COLORS.primary,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  categoryText: {
    color: COLORS.white,
    fontSize: 14,
    fontWeight: 'bold',
  },
  detailsSection: {
    marginTop: 20,
    display: 'flex',
    flexDirection: 'column',
    gap: 10,
  },
  headingText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.black,
    marginBottom: 12,
  },
  subText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 12,
  },
  addressText: {
    fontSize: 14,
    color: '#444',
    marginBottom: 8,
  },
  yearsInBusiness: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  inputHeading: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.black,
    marginBottom: 10,
  },
  locationField: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  locationText: {
    fontSize: 16,
    color: '#444',
    marginLeft: 12,
  },
  contactHeading: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.black,
    marginBottom: 10,
  },
  contactSection: {
    flexDirection: 'row',
    gap: 20,
  },
  iconWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  actionText: {
    fontSize: 14,
    color: COLORS.black,
    marginLeft: 8,
  },
  additionalDetailsText: {
    fontSize: 14,
    color: '#444',
    marginBottom: 16,
    fontStyle: 'italic',
  },
});

export default DetailsBusiness;
