import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  Pressable,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { BACK_ICON } from '../../utils/image-constants';
import { useSelector } from 'react-redux';
import COLORS from '../../themes/colors';
import Icon from 'react-native-vector-icons/FontAwesome';
import ROUTES from '../../navigation/RouteConstants';

type CustomHeaderProps = {
  title: string;
  showBackButton?: boolean;
  userName?: string;
};

const CustomHeader: React.FC<CustomHeaderProps> = ({
  title,
  showBackButton = true,
  userName,
}) => {
  const navigation = useNavigation();
  const route = useRoute();
  const isGuestDashboard = route.name === 'GUEST_DASHBOARD';
  const isDashboard = route.name === 'DASHBOARD';
  const isProfileSetup = route.name === 'PROFILE_SETUP';
  const handleNavigateProfile = () => {
    navigation.navigate('PROFILE');
  };
  const userDetails = useSelector((state: any) => state.auth.userDetails);
  const token = useSelector((state: any) => state.auth.token);
  console.log('user details', userDetails);

  const handleNavigateDashboard = () => {
    navigation.navigate(ROUTES.DASHBOARD);
  };
  const handleNavigateLogin = () => {
    navigation.navigate(ROUTES.LOGIN)
  }


  return (
    <View style={styles.container}>
      {showBackButton && !isDashboard && !isProfileSetup && (
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}>
          <Image source={BACK_ICON} style={styles.backIcon} />
        </TouchableOpacity>
      )}

      {isProfileSetup ? (
        <View style={styles.userNameOuter}>
          <Text style={styles.userName}>{userName}</Text>
        </View>
      ) : isDashboard ? (
        <Pressable
          style={styles.dashboardHeader}
          onPress={handleNavigateProfile}>
          <View style={styles.imageContainer}>
            <Image
              // source={AVATAR_DEFAULT}
              source={{
                uri: userDetails?.user_image || 'https://i.pravatar.cc/150',
              }}
              style={styles.profileIcon}
            />
          </View>
          <Text style={styles.userNameA}>Hello, {userDetails?.name}</Text>
        </Pressable>
      ) :
        isGuestDashboard ? (
          <View
            style={styles.guestdashboardHeader}>

            <Text style={styles.hello}>HELLO</Text>
            <Text style={styles.guest}>Guest</Text>
            <Pressable style={styles.login} onPress={handleNavigateLogin}>

              <Text style={styles.loginText}>Login</Text>
            </Pressable>
          </View>
        ) : (
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{title}</Text>
            {token && (
              <Icon
                name="home"
                size={28}
                style={styles.homeIcon}
                onPress={handleNavigateDashboard}
              />
            )}
          </View>
        )}
    </View>
  );
};

export default CustomHeader;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    elevation: 3,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    position: 'relative',
  },
  backButton: {
    marginRight: 15,
    position: 'absolute',
    left: 20,
    zIndex: 10,
  },
  dashboardHeader: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },

  login: {
    borderWidth: 1,
    borderColor: COLORS.primary,
    backgroundColor: COLORS.secondary,
    paddingHorizontal: 8,
    paddingVertical: 5,
    borderRadius: 10,

  },
  loginText: {
    color: COLORS.primary
  },
  imageContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: COLORS.primary,
    overflow: 'hidden',
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileIcon: {
    width: '100%',
    height: '100%',
    borderRadius: 30,
  },

  // profileIcon: {
  //   width: 50,
  //   height: 50,
  //   borderRadius: 25,
  //   marginRight: 10,
  // },
  userNameOuter: {
    flex: 1,
    alignItems: 'center',
  },
  userName: {
    paddingVertical: 10,
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.black,
  },
  userNameA: {
    paddingVertical: 10,
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.black,
  },
  guest: {
    paddingVertical: 10,
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.black,
    flex: 1
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.black,
    alignSelf: 'center',
  },
  backIcon: {
    marginVertical: 10,
  },
  homeIcon: {
    position: 'absolute',
    right: 0,
  },
});
