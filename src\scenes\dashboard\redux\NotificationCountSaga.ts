import { all, call, put } from "redux-saga/effects";
import { API_URL } from "../../../services/webConstants";
import { apiClient } from "../../../services/httpServices";
import { allNotificationsFail, allNotificationsStarted, allNotificationsSuccess } from "./NotifictionSlice";
import { notificationCountFail, notificationCountStarted, notificationCountSuccess, } from "./NotificationCountSlice";
import AsyncStorage from "@react-native-async-storage/async-storage";


export function* notificationsCountSaga() {

    yield put(notificationCountStarted());

    const { data, ok } = yield call(apiClient.get, API_URL.NOTIFICATION_COUNT);
    if (ok && data) {
        yield put(notificationCountSuccess(data.user_alert));
        yield AsyncStorage.setItem("user_alert_count", data.user_alert);
    } else if (!ok && data) {
        yield put(notificationCountFail());
    } else {
        yield put(notificationCountFail());
    }
}