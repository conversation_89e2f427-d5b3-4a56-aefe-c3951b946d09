import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  useWindowDimensions,
} from 'react-native';
import {PUNJABI_ICON} from '../../utils/image-constants';
import COLORS from '../../themes/colors';
import {useNavigation} from '@react-navigation/native';
import CustomHeader from '../../components/molecules/CustomHeader';
import {useSelector} from 'react-redux';
import he from 'he';
import RenderHTML from 'react-native-render-html';

const TermsAndConditionsScreen = () => {
  const navigation = useNavigation();

  const {width} = useWindowDimensions();

  // Fetch settings data from Redux
  const {settings} = useSelector(state => state.settings);
  console.log('settings from contact us screen', settings?.terms_conditions);

  const encodedHTML = settings?.terms_conditions || {};
  // Decode the HTML entities (like &lt;, &gt;, etc.)
  const decodedHTML = he.decode(encodedHTML);

  const source = {
    html: decodedHTML,
  };

  return (
    <View style={styles.container}>
      <CustomHeader title="Terms & Conditions" showBackButton={true} />

      {/* Logo */}
      <View style={styles.logoContainer}>
        <Image source={PUNJABI_ICON} style={styles.brandLogo} />
      </View>

      {/* Main Content */}
      <ScrollView
        contentContainerStyle={styles.contentWrapper}
        showsVerticalScrollIndicator={false}>
        <View style={styles.contentSection}>
          <Text style={styles.title}>Terms and Conditions</Text>
          {/* Terms and Conditions Content */}
          <View style={styles.section}>
            <RenderHTML contentWidth={width} source={source} />
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default TermsAndConditionsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: 40,
    backgroundColor: COLORS.secondary,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 40,
  },
  brandLogo: {
    width: 150,
    height: 120,
    resizeMode: 'contain',
  },
  contentWrapper: {
    flexGrow: 1,
    paddingHorizontal: 20,
  },
  contentSection: {
    marginTop: 20,
  },
  title: {
    fontSize: 28,
    color: '#333',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  section: {
    marginBottom: 20,
  },
});
